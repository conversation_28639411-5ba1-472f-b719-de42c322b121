/**
 * @file mlx90640_instance.c
 * @brief Single MLX90640 sensor instance implementation
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include "mlx90640_instance.h"
#include "mlx90640_i2c_multi.h"
#include "MLX90640_API.h"
#include "MLX90640_I2C_Driver.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "MLX90640_INSTANCE";

// ============================================================================
// Instance Management Functions
// ============================================================================

esp_err_t mlx90640_instance_init(mlx90640_instance_t* instance,
                                sensor_id_t sensor_id,
                                float* temp_data,
                                thermal_detector_t* detector,
                                const thermal_config_t* config)
{
    if (instance == NULL || temp_data == NULL || detector == NULL || config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (sensor_id >= SENSOR_COUNT) {
        return ESP_ERR_INVALID_ARG;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Initializing sensor instance %s", 
                     mlx90640_get_sensor_name(sensor_id));
    
    // Clear instance structure
    memset(instance, 0, sizeof(mlx90640_instance_t));
    
    // Set basic configuration
    instance->sensor_id = sensor_id;
    instance->bus_type = sensor_configs[sensor_id].bus_type;
    instance->device_addr = sensor_configs[sensor_id].device_addr;
    instance->temp_data = temp_data;
    instance->detector = detector;
    
    // Create data queue
    instance->data_queue = xQueueCreate(DATA_QUEUE_LENGTH, DATA_QUEUE_ITEM_SIZE);
    if (instance->data_queue == NULL) {
        MULTI_SENSOR_LOGE(TAG, "Failed to create data queue for sensor %s", 
                         mlx90640_get_sensor_name(sensor_id));
        return ESP_ERR_NO_MEM;
    }
    
    // Create data mutex
    instance->data_mutex = xSemaphoreCreateMutex();
    if (instance->data_mutex == NULL) {
        MULTI_SENSOR_LOGE(TAG, "Failed to create data mutex for sensor %s", 
                         mlx90640_get_sensor_name(sensor_id));
        vQueueDelete(instance->data_queue);
        return ESP_ERR_NO_MEM;
    }
    
    // Initialize thermal detector
    int ret = thermal_detector_init(instance->detector, config, NULL);
    if (ret != 0) {
        MULTI_SENSOR_LOGE(TAG, "Failed to initialize thermal detector for sensor %s: %d", 
                         mlx90640_get_sensor_name(sensor_id), ret);
        vSemaphoreDelete(instance->data_mutex);
        vQueueDelete(instance->data_queue);
        return ESP_FAIL;
    }
    
    // Initialize MLX90640 hardware
    esp_err_t hw_ret = mlx90640_instance_init_hardware(instance);
    if (hw_ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Failed to initialize hardware for sensor %s: %s", 
                         mlx90640_get_sensor_name(sensor_id), esp_err_to_name(hw_ret));
        vSemaphoreDelete(instance->data_mutex);
        vQueueDelete(instance->data_queue);
        return hw_ret;
    }
    
    instance->is_initialized = true;
    MULTI_SENSOR_LOGI(TAG, "Sensor instance %s initialized successfully", 
                     mlx90640_get_sensor_name(sensor_id));
    
    return ESP_OK;
}

esp_err_t mlx90640_instance_deinit(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Deinitializing sensor instance %s", 
                     mlx90640_instance_get_name(instance));
    
    // Stop tasks if running
    mlx90640_instance_stop_tasks(instance);
    
    // Clean up synchronization objects
    if (instance->data_mutex != NULL) {
        vSemaphoreDelete(instance->data_mutex);
        instance->data_mutex = NULL;
    }
    
    if (instance->data_queue != NULL) {
        vQueueDelete(instance->data_queue);
        instance->data_queue = NULL;
    }
    
    instance->is_initialized = false;
    instance->is_active = false;
    
    MULTI_SENSOR_LOGI(TAG, "Sensor instance %s deinitialized", 
                     mlx90640_instance_get_name(instance));
    
    return ESP_OK;
}

esp_err_t mlx90640_instance_start_tasks(mlx90640_instance_t* instance)
{
    if (instance == NULL || !instance->is_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (instance->is_active) {
        MULTI_SENSOR_LOGW(TAG, "Tasks already running for sensor %s", 
                         mlx90640_instance_get_name(instance));
        return ESP_OK;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Starting tasks for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    // Create acquisition task name
    char acquire_task_name[32];
    snprintf(acquire_task_name, sizeof(acquire_task_name), "acquire_%s", 
             mlx90640_get_sensor_name(instance->sensor_id));
    
    // Determine task parameters based on I2C type
    uint32_t stack_size = SENSOR_IS_SOFTWARE_I2C(instance->sensor_id) ?
                         ACQUIRE_TASK_STACK_SW : ACQUIRE_TASK_STACK_HW;
    uint32_t priority = SENSOR_IS_SOFTWARE_I2C(instance->sensor_id) ?
                       ACQUIRE_TASK_PRIORITY_SW : ACQUIRE_TASK_PRIORITY_HW;

    // Create acquisition task on Core 0
    BaseType_t ret = xTaskCreatePinnedToCore(
        mlx90640_acquire_task,
        acquire_task_name,
        stack_size,
        instance,
        priority,
        &instance->acquire_task,
        0  // Core 0
    );
    
    if (ret != pdPASS) {
        MULTI_SENSOR_LOGE(TAG, "Failed to create acquisition task for sensor %s", 
                         mlx90640_instance_get_name(instance));
        return ESP_ERR_NO_MEM;
    }
    
    // Create processing task name
    char process_task_name[32];
    snprintf(process_task_name, sizeof(process_task_name), "process_%s", 
             mlx90640_get_sensor_name(instance->sensor_id));
    
    // Create processing task on Core 1
    ret = xTaskCreatePinnedToCore(
        mlx90640_process_task,
        process_task_name,
        PROCESS_TASK_STACK,
        instance,
        PROCESS_TASK_PRIORITY,
        &instance->process_task,
        1  // Core 1
    );
    
    if (ret != pdPASS) {
        MULTI_SENSOR_LOGE(TAG, "Failed to create processing task for sensor %s", 
                         mlx90640_instance_get_name(instance));
        // Clean up acquisition task
        vTaskDelete(instance->acquire_task);
        instance->acquire_task = NULL;
        return ESP_ERR_NO_MEM;
    }
    
    instance->is_active = true;
    MULTI_SENSOR_LOGI(TAG, "Tasks started successfully for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    return ESP_OK;
}

esp_err_t mlx90640_instance_stop_tasks(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!instance->is_active) {
        return ESP_OK;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Stopping tasks for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    instance->is_active = false;
    
    // Delete tasks
    if (instance->acquire_task != NULL) {
        vTaskDelete(instance->acquire_task);
        instance->acquire_task = NULL;
    }
    
    if (instance->process_task != NULL) {
        vTaskDelete(instance->process_task);
        instance->process_task = NULL;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Tasks stopped for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    return ESP_OK;
}

bool mlx90640_instance_is_healthy(const mlx90640_instance_t* instance)
{
    if (instance == NULL || !instance->is_initialized) {
        return false;
    }
    
    // Check error rate
    if (instance->frame_count > 0) {
        float error_rate = (float)instance->error_count / instance->frame_count;
        if (error_rate > 0.1f) {  // More than 10% error rate
            return false;
        }
    }
    
    // Check if tasks are still running
    if (instance->is_active) {
        if (instance->acquire_task == NULL || instance->process_task == NULL) {
            return false;
        }
    }
    
    return true;
}

// ============================================================================
// Hardware Interface Functions
// ============================================================================

/**
 * @brief Initialize MLX90640-specific hardware configuration (instance-level)
 *
 * This function handles MLX90640-specific initialization that requires
 * the sensor to be already detected and basic I2C communication established.
 *
 * Performs:
 * - EEPROM data extraction
 * - Parameter extraction and validation
 * - Refresh rate configuration
 * - Chess pattern mode setup
 *
 * Note: Basic sensor detection is already done in system-level initialization.
 */
esp_err_t mlx90640_instance_init_hardware(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    MULTI_SENSOR_LOGI(TAG, "Configuring MLX90640-specific hardware for sensor %s",
                     mlx90640_instance_get_name(instance));

    // Skip sensor detection - already verified in system-level initialization
    // Proceed directly to MLX90640-specific configuration

    // Set I2C context for MLX90640 library to use the correct bus
    if (!MLX90640_SetI2CContext(instance->bus_type)) {
        MULTI_SENSOR_LOGE(TAG, "Failed to set I2C context for sensor %s (bus %d)",
                         mlx90640_instance_get_name(instance), instance->bus_type);
        return ESP_FAIL;
    }

    // Extract MLX90640 parameters (EEPROM data)
    uint16_t eeMLX90640[832];
    int status = MLX90640_DumpEE(instance->device_addr, eeMLX90640);
    if (status != 0) {
        MULTI_SENSOR_LOGE(TAG, "Failed to dump EEPROM for sensor %s: %d", 
                         mlx90640_instance_get_name(instance), status);
        return ESP_FAIL;
    }
    
    status = MLX90640_ExtractParameters(eeMLX90640, &instance->mlx90640_params);
    if (status != 0) {
        MULTI_SENSOR_LOGE(TAG, "Failed to extract parameters for sensor %s: %d", 
                         mlx90640_instance_get_name(instance), status);
        return ESP_FAIL;
    }
    
    // Set refresh rate (2 Hz for better stability)
    // todo 使用宏定义的freshrate
    // todo \mlx90640_lcd_display.c 中包含了一些常用的定义，迁移到一个通用的头文件中，然后再这里使用
    status = MLX90640_SetRefreshRate(instance->device_addr, 0x02);
    if (status != 0) {
        MULTI_SENSOR_LOGW(TAG, "Failed to set refresh rate for sensor %s: %d", 
                         mlx90640_instance_get_name(instance), status);
    }
    
    // Set chess pattern mode
    status = MLX90640_SetChessMode(instance->device_addr);
    if (status != 0) {
        MULTI_SENSOR_LOGW(TAG, "Failed to set chess mode for sensor %s: %d", 
                         mlx90640_instance_get_name(instance), status);
    }
    
    MULTI_SENSOR_LOGI(TAG, "MLX90640 hardware initialized for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    return ESP_OK;
}

esp_err_t mlx90640_instance_read_frame(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    // Set I2C context for MLX90640 library to use the correct bus
    if (!MLX90640_SetI2CContext(instance->bus_type)) {
        MULTI_SENSOR_LOGE(TAG, "Failed to set I2C context for sensor %s (bus %d)",
                         mlx90640_instance_get_name(instance), instance->bus_type);
        return ESP_FAIL;
    }

    int status = MLX90640_GetFrameData(instance->device_addr, instance->mlx90640_frame);
    if (status < 0) {
        instance->error_count++;
        MULTI_SENSOR_LOGD(TAG, "Failed to read frame from sensor %s: %d", 
                         mlx90640_instance_get_name(instance), status);
        return ESP_FAIL;
    }
    
    return ESP_OK;
}

esp_err_t mlx90640_instance_calculate_temperatures(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // Get ambient temperature
    float vdd = MLX90640_GetVdd(instance->mlx90640_frame, &instance->mlx90640_params);
    float ta = MLX90640_GetTa(instance->mlx90640_frame, &instance->mlx90640_params);
    
    // Calculate temperatures for all pixels
    MLX90640_CalculateTo(instance->mlx90640_frame, &instance->mlx90640_params, 
                        0.95f, ta - 8.0f, instance->temp_data);
    
    return ESP_OK;
}

// ============================================================================
// Task Functions
// ============================================================================

void mlx90640_acquire_task(void* param)
{
    mlx90640_instance_t* instance = (mlx90640_instance_t*)param;
    uint32_t notification = 1;
    
    MULTI_SENSOR_LOGI(TAG, "Acquisition task started for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    while (instance->is_active) {
        // Read frame data from MLX90640
        esp_err_t ret = mlx90640_instance_read_frame(instance);
        if (ret == ESP_OK) {
            // Calculate temperatures
            ret = mlx90640_instance_calculate_temperatures(instance);
            if (ret == ESP_OK) {
                // Notify processing task
                xQueueSend(instance->data_queue, &notification, 0);
            }
        }
        
        // Control frame rate
        vTaskDelay(FRAME_INTERVAL_MS / portTICK_PERIOD_MS);
    }
    
    MULTI_SENSOR_LOGI(TAG, "Acquisition task stopped for sensor %s", 
                     mlx90640_instance_get_name(instance));
    vTaskDelete(NULL);
}

void mlx90640_process_task(void* param)
{
    mlx90640_instance_t* instance = (mlx90640_instance_t*)param;
    uint32_t notification;
    
    MULTI_SENSOR_LOGI(TAG, "Processing task started for sensor %s", 
                     mlx90640_instance_get_name(instance));
    
    while (instance->is_active) {
        // Wait for data ready notification
        if (xQueueReceive(instance->data_queue, &notification, portMAX_DELAY)) {
            // Get data access permission
            if (xSemaphoreTake(instance->data_mutex, pdMS_TO_TICKS(10))) {
                // Start timing measurement
                int64_t start_time = esp_timer_get_time();
                
                // Process thermal detection
                thermal_result_t result;
                int ret = thermal_detector_process_frame_1d_with_learning(
                    instance->detector, instance->temp_data, &result);
                
                // End timing measurement
                int64_t end_time = esp_timer_get_time();
                instance->last_process_time_us = (uint32_t)(end_time - start_time);
                
                if (ret == 0) {
                    instance->frame_count++;
                    
                    // Log results based on state and detection
                    if (result.current_state == STATE_JUDGING && result.anomaly_detected) {
                        MULTI_SENSOR_LOGI(TAG, "Sensor %s Frame %"PRIu32": ANOMALY at (%.1f, %.1f)", 
                                         mlx90640_instance_get_name(instance),
                                         result.frame_count,
                                         result.anomaly_center.x, result.anomaly_center.y);
                        
                        if (result.trigger_anomaly) {
                            MULTI_SENSOR_LOGW(TAG, "*** Sensor %s THERMAL ANOMALY TRIGGERED! ***", 
                                             mlx90640_instance_get_name(instance));
                        }
                    }
                } else {
                    instance->error_count++;
                }
                
                // Release data access permission
                xSemaphoreGive(instance->data_mutex);
            }
        }
    }
    
    MULTI_SENSOR_LOGI(TAG, "Processing task stopped for sensor %s", 
                     mlx90640_instance_get_name(instance));
    vTaskDelete(NULL);
}

// ============================================================================
// Utility Functions
// ============================================================================

const char* mlx90640_instance_get_name(const mlx90640_instance_t* instance)
{
    if (instance == NULL || instance->sensor_id >= SENSOR_COUNT) {
        return "UNKNOWN";
    }
    return mlx90640_get_sensor_name(instance->sensor_id);
}

bool mlx90640_instance_validate(const mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return false;
    }
    
    if (instance->sensor_id >= SENSOR_COUNT) {
        return false;
    }
    
    if (instance->temp_data == NULL || instance->detector == NULL) {
        return false;
    }
    
    return true;
}

void mlx90640_instance_log_status(const mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        MULTI_SENSOR_LOGE(TAG, "Instance is NULL");
        return;
    }

    const char* sensor_name = mlx90640_instance_get_name(instance);

    MULTI_SENSOR_LOGI(TAG, "Sensor %s Status:", sensor_name);
    MULTI_SENSOR_LOGI(TAG, "  Initialized: %s", instance->is_initialized ? "Yes" : "No");
    MULTI_SENSOR_LOGI(TAG, "  Active: %s", instance->is_active ? "Yes" : "No");
    MULTI_SENSOR_LOGI(TAG, "  Frame Count: %u", (unsigned)instance->frame_count);
    MULTI_SENSOR_LOGI(TAG, "  Error Count: %u", (unsigned)instance->error_count);
    MULTI_SENSOR_LOGI(TAG, "  Last Process Time: %u us", (unsigned)instance->last_process_time_us);
    MULTI_SENSOR_LOGI(TAG, "  I2C Bus: %s", mlx90640_get_i2c_bus_name(instance->bus_type));
    MULTI_SENSOR_LOGI(TAG, "  Device Address: 0x%02X", instance->device_addr);
    MULTI_SENSOR_LOGI(TAG, "  Acquire Task: %s", instance->acquire_task ? "Running" : "Stopped");
    MULTI_SENSOR_LOGI(TAG, "  Process Task: %s", instance->process_task ? "Running" : "Stopped");
}
