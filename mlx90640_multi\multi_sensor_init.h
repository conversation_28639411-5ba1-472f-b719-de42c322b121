/**
 * @file multi_sensor_init.h
 * @brief Multi-sensor system initialization sequence
 * 
 * This file defines the complete initialization sequence for the 4-sensor
 * MLX90640 system, including GPIO setup, I2C initialization, sensor address
 * modification, and hardware initialization.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MULTI_SENSOR_INIT_H
#define MULTI_SENSOR_INIT_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "mlx90640_multi_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// Initialization Sequence Functions
// ============================================================================

/**
 * @brief System-level hardware initialization sequence (simplified)
 *
 * This function performs system-level hardware initialization:
 * 1. Initialize 4 independent I2C buses (2 hardware + 2 software)
 * 2. Basic sensor detection on all buses
 *
 * Note: MLX90640-specific configuration (EEPROM, refresh rate, modes)
 * is handled in mlx90640_instance_init_hardware() during instance initialization.
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_hardware_init(void);

/**
 * @brief Initialize all 4 I2C buses (2 hardware + 2 software)
 *
 * Initializes:
 * - Hardware I2C Bus 0 (GPIO 21,22)
 * - Hardware I2C Bus 1 (GPIO 18,19)
 * - Software I2C Bus 2 (GPIO 4,5)
 * - Software I2C Bus 3 (GPIO 16,17)
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_i2c_init(void);

/**
 * @brief Detect all sensors on their dedicated I2C buses
 *
 * This function detects all 4 sensors on their independent I2C buses:
 * - Sensor 0: Hardware I2C Bus 0 (GPIO 21,22), Address 0x33
 * - Sensor 1: Hardware I2C Bus 1 (GPIO 18,19), Address 0x33
 * - Sensor 2: Software I2C Bus 2 (GPIO 4,5), Address 0x33
 * - Sensor 3: Software I2C Bus 3 (GPIO 16,17), Address 0x33
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_detect_all_sensors(void);

/**
 * @brief Initialize all sensor hardware
 *
 * Initializes the MLX90640 hardware for all 4 sensors on their
 * dedicated I2C buses. No address modification needed.
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_hardware_setup(void);

/**
 * @brief Verify communication with all sensors
 *
 * Verifies that all 4 sensors are responding correctly on their
 * dedicated I2C buses.
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_verify_all_sensors(void);

// ============================================================================
// Individual Initialization Steps
// ============================================================================

/**
 * @brief Basic detection for a single sensor (system-level)
 *
 * Performs basic communication test with a single MLX90640 sensor.
 * This is part of system-level initialization and only verifies
 * that the sensor responds to I2C communication.
 *
 * MLX90640-specific configuration is handled in mlx90640_instance_init_hardware().
 *
 * @param sensor_id Sensor ID (SENSOR_0 to SENSOR_3)
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_init_single_sensor(sensor_id_t sensor_id);

// ============================================================================
// Verification Functions
// ============================================================================

/**
 * @brief Verify all sensors are responding
 * 
 * Checks that all 4 sensors are responding at their expected
 * I2C addresses after initialization.
 * 
 * @return ESP_OK if all sensors respond, error code otherwise
 */
esp_err_t multi_sensor_verify_all_sensors(void);

/**
 * @brief Verify sensor at specific address
 * 
 * @param sensor_id Sensor to verify
 * @return ESP_OK if sensor responds, error code otherwise
 */
esp_err_t multi_sensor_verify_sensor(sensor_id_t sensor_id);

/**
 * @brief Test I2C communication with all sensors
 * 
 * Performs basic I2C communication test with all sensors
 * to ensure they are properly initialized and responding.
 * 
 * @return ESP_OK if all tests pass, error code otherwise
 */
esp_err_t multi_sensor_test_communication(void);

// ============================================================================
// Cleanup Functions
// ============================================================================

/**
 * @brief Deinitialize hardware resources
 * 
 * Cleans up all hardware resources including I2C buses and GPIO pins.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_hardware_deinit(void);

/**
 * @brief Reset all sensors to default state
 * 
 * Resets all sensors to their default configuration and
 * powers off Ay and By via analog switches.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_reset_all(void);

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * @brief Get initialization step name for logging
 * 
 * @param step Step number (0-based)
 * @return String description of initialization step
 */
const char* multi_sensor_get_init_step_name(uint8_t step);

/**
 * @brief Log initialization progress
 * 
 * @param step Current step number
 * @param total_steps Total number of steps
 * @param step_name Name of current step
 */
void multi_sensor_log_init_progress(uint8_t step, uint8_t total_steps, const char* step_name);

/**
 * @brief Delay with logging for initialization steps
 * 
 * @param delay_ms Delay in milliseconds
 * @param reason Reason for delay (for logging)
 */
void multi_sensor_init_delay(uint32_t delay_ms, const char* reason);

// ============================================================================
// Error Recovery Functions
// ============================================================================

/**
 * @brief Retry initialization step with backoff
 * 
 * @param init_func Function to retry
 * @param max_retries Maximum number of retries
 * @param step_name Name of step for logging
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_retry_init_step(esp_err_t (*init_func)(void), 
                                      uint8_t max_retries, 
                                      const char* step_name);

/**
 * @brief Handle initialization failure
 * 
 * @param error_code Error code from failed step
 * @param step_name Name of failed step
 * @param recovery_possible Whether recovery is possible
 * @return ESP_OK if recovery successful, error code otherwise
 */
esp_err_t multi_sensor_handle_init_failure(esp_err_t error_code, 
                                          const char* step_name, 
                                          bool recovery_possible);

#ifdef __cplusplus
}
#endif

#endif // MULTI_SENSOR_INIT_H
