/**
 * @file test_unified_i2c_integration.c
 * @brief Test file to verify MLX90640 library integration with unified I2C interface
 * 
 * This test demonstrates that the MLX90640 library can now work with all 4 I2C buses
 * (2 hardware + 2 software) through the unified I2C interface.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-23
 */

#include <stdio.h>
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// MLX90640 and unified I2C includes
#include "MLX90640_I2C_Driver.h"
#include "MLX90640_API.h"
#include "mlx90640_i2c_multi.h"
#include "mlx90640_multi_config.h"

static const char *TAG = "UNIFIED_I2C_TEST";

/**
 * @brief Test MLX90640 library with unified I2C interface on all 4 buses
 * 
 * This function tests that the MLX90640 library can successfully communicate
 * with sensors on all 4 I2C buses using the context-based routing system.
 */
void test_mlx90640_unified_i2c_integration(void)
{
    ESP_LOGI(TAG, "=== MLX90640 Unified I2C Integration Test ===");
    
    // Initialize the unified I2C interface
    ESP_LOGI(TAG, "Initializing unified I2C interface...");
    if (!MLX90640_I2CInit_Unified()) {
        ESP_LOGE(TAG, "Failed to initialize unified I2C interface");
        return;
    }
    
    // Test each I2C bus
    i2c_bus_type_t test_buses[] = {
        I2C_BUS_HARDWARE_0,  // Hardware I2C Bus 0
        I2C_BUS_HARDWARE_1,  // Hardware I2C Bus 1
        I2C_BUS_SOFTWARE_2,  // Software I2C Bus 2
        I2C_BUS_SOFTWARE_3   // Software I2C Bus 3
    };
    
    const char* bus_names[] = {
        "Hardware I2C Bus 0",
        "Hardware I2C Bus 1", 
        "Software I2C Bus 2",
        "Software I2C Bus 3"
    };
    
    uint8_t slave_addr = MLX90640_ADDR_UNIFIED;  // 0x33
    
    for (int i = 0; i < 4; i++) {
        ESP_LOGI(TAG, "\n--- Testing %s ---", bus_names[i]);
        
        // Set I2C context for this bus
        if (!MLX90640_SetI2CContext(test_buses[i])) {
            ESP_LOGE(TAG, "Failed to set I2C context for %s", bus_names[i]);
            continue;
        }
        
        ESP_LOGI(TAG, "I2C context set to bus type %d", test_buses[i]);
        
        // Test basic I2C communication by reading a register
        uint16_t test_data;
        int result = MLX90640_I2CRead(slave_addr, 0x2407, 1, &test_data);
        
        if (result == 0) {
            ESP_LOGI(TAG, "✓ %s: MLX90640_I2CRead successful (data: 0x%04X)", 
                     bus_names[i], test_data);
            
            // Test write operation (read current value, write it back)
            uint16_t current_value;
            result = MLX90640_I2CRead(slave_addr, 0x800D, 1, &current_value);
            if (result == 0) {
                result = MLX90640_I2CWrite(slave_addr, 0x800D, current_value);
                if (result == 0) {
                    ESP_LOGI(TAG, "✓ %s: MLX90640_I2CWrite successful", bus_names[i]);
                } else {
                    ESP_LOGE(TAG, "✗ %s: MLX90640_I2CWrite failed (%d)", bus_names[i], result);
                }
            }
            
            // Test MLX90640_GetFrameData (if sensor is properly configured)
            uint16_t frame_data[834];
            result = MLX90640_GetFrameData(slave_addr, frame_data);
            if (result == 0) {
                ESP_LOGI(TAG, "✓ %s: MLX90640_GetFrameData successful", bus_names[i]);
            } else {
                ESP_LOGW(TAG, "⚠ %s: MLX90640_GetFrameData failed (%d) - sensor may need configuration", 
                         bus_names[i], result);
            }
            
        } else {
            ESP_LOGE(TAG, "✗ %s: MLX90640_I2CRead failed (%d) - sensor not responding", 
                     bus_names[i], result);
        }
        
        // Small delay between tests
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    ESP_LOGI(TAG, "\n=== Integration Test Complete ===");
    ESP_LOGI(TAG, "The MLX90640 library can now route I2C calls to all 4 buses:");
    ESP_LOGI(TAG, "  - Hardware I2C buses for sensors 0 and 1");
    ESP_LOGI(TAG, "  - Software I2C buses for sensors 2 and 3");
    ESP_LOGI(TAG, "Context-based routing enables multi-sensor operation!");
}

/**
 * @brief Test the I2C context management functions
 */
void test_i2c_context_management(void)
{
    ESP_LOGI(TAG, "\n=== I2C Context Management Test ===");
    
    // Test setting and getting context for each bus type
    i2c_bus_type_t test_buses[] = {
        I2C_BUS_HARDWARE_0, I2C_BUS_HARDWARE_1, 
        I2C_BUS_SOFTWARE_2, I2C_BUS_SOFTWARE_3
    };
    
    for (int i = 0; i < 4; i++) {
        // Set context
        bool success = MLX90640_SetI2CContext(test_buses[i]);
        if (success) {
            // Get context and verify
            i2c_bus_type_t current_context = MLX90640_GetI2CContext();
            if (current_context == test_buses[i]) {
                ESP_LOGI(TAG, "✓ Context management test passed for bus %d", test_buses[i]);
            } else {
                ESP_LOGE(TAG, "✗ Context mismatch: set %d, got %d", test_buses[i], current_context);
            }
        } else {
            ESP_LOGE(TAG, "✗ Failed to set context for bus %d", test_buses[i]);
        }
    }
    
    // Test invalid bus type
    bool success = MLX90640_SetI2CContext((i2c_bus_type_t)99);
    if (!success) {
        ESP_LOGI(TAG, "✓ Invalid bus type correctly rejected");
    } else {
        ESP_LOGE(TAG, "✗ Invalid bus type was accepted");
    }
    
    ESP_LOGI(TAG, "=== Context Management Test Complete ===");
}

/**
 * @brief Main test function - call this from your main application
 */
void run_unified_i2c_integration_tests(void)
{
    ESP_LOGI(TAG, "Starting MLX90640 Unified I2C Integration Tests...");
    
    // Test 1: I2C context management
    test_i2c_context_management();
    
    // Test 2: MLX90640 library integration with unified I2C
    test_mlx90640_unified_i2c_integration();
    
    ESP_LOGI(TAG, "All integration tests completed!");
}
