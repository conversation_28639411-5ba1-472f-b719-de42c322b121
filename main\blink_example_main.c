/* Blink Example

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/
#include <stdio.h>
#include <string.h>
#include <inttypes.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "driver/uart.h"
#include "esp_log.h"
#include "esp_timer.h"
// #include "led_strip.h"
#include "sdkconfig.h"
#include "MLX90640_I2C_Driver.h"
#include "mlx90640_lcd_display.h"
#include "../thermalDetection/thermal_anomaly_core.h"

static const char *TAG = "example";

// UART configuration
#define UART_PORT_NUM      UART_NUM_0
#define UART_BAUD_RATE     400000
#define UART_DATA_BITS     UART_DATA_8_BITS
#define UART_PARITY        UART_PARITY_DISABLE
#define UART_STOP_BITS     UART_STOP_BITS_1
#define UART_FLOW_CTRL     UART_HW_FLOWCTRL_DISABLE
#define UART_SOURCE_CLK    UART_SCLK_DEFAULT

// Data frame protocol constants
#define SYNC_BYTE_1        0xAA
#define SYNC_BYTE_2        0x55
#define TEMP_DATA_SIZE     (32 * 24)  // 768 temperature values
#define PAYLOAD_SIZE       (TEMP_DATA_SIZE * sizeof(float))  // 3072 bytes
#define FRAME_SIZE         (2 + 2 + PAYLOAD_SIZE + 1)  // sync + length + payload + checksum

// Global thermal detector instance
static thermal_detector_t thermal_detector;

// Function prototypes
static void uart_init(void);
static void thermal_detector_init_esp32(void);
static void process_thermal_anomaly_detection(void);
static void transmit_thermal_result(const thermal_result_t* result);
static uint8_t calculate_checksum(const uint8_t* data, size_t length);

// UART initialization function
static void uart_init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_BITS,
        .parity = UART_PARITY,
        .stop_bits = UART_STOP_BITS,
        .flow_ctrl = UART_FLOW_CTRL,
        .source_clk = UART_SOURCE_CLK,
    };

    // Install UART driver
    ESP_ERROR_CHECK(uart_driver_install(UART_PORT_NUM, 1024 * 2, 0, 0, NULL, 0));
    ESP_ERROR_CHECK(uart_param_config(UART_PORT_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_PORT_NUM, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE,
                                  UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));

    ESP_LOGI(TAG, "UART initialized successfully");
}

// Initialize thermal anomaly detector with ESP32-optimized settings and learning support
static void thermal_detector_init_esp32(void)
{
    thermal_config_t config;

    // Create ESP32-optimized configuration
    thermal_detector_create_default_config(&config);

    // ESP32-specific optimizations
    config.enable_pre_filter = true;
    config.pre_filter_min = 0.0f;      // °C
    config.pre_filter_max = 60.0f;     // °C
    config.pre_filter_delta = 40.0f;   // °C
    config.window_size = 15;           // Reduced for ESP32 memory constraints
    config.center_distance_threshold = 15.0f;  // pixels
    config.min_anomaly_pixels = 2;     // Minimum anomaly pixels for detection

    // Learning stage configuration
    config.enable_learning = true;     // Enable learning stage
    config.warmup_time_ms = 3000;      // 3 seconds warmup for ESP32

    // Initialize detector with learning support
    // Use NULL for thresholds (will use default from pixel_thresholds.h)
    // Use NULL for pre_thresholds (will use same as thresholds)
    int ret = thermal_detector_init(&thermal_detector, &config, NULL);
    if (ret != 0) {
        ESP_LOGE(TAG, "Failed to initialize thermal detector: %d", ret);
        return;
    }

    ESP_LOGI(TAG, "Thermal anomaly detector initialized successfully with learning support");
    ESP_LOGI(TAG, "  - Window size: %d frames", (int)config.window_size);
    ESP_LOGI(TAG, "  - Min anomaly pixels: %d", (int)config.min_anomaly_pixels);
    ESP_LOGI(TAG, "  - Center distance threshold: %.1f pixels", config.center_distance_threshold);
    ESP_LOGI(TAG, "  - Pre-filter enabled: %s", config.enable_pre_filter ? "Yes" : "No");
    ESP_LOGI(TAG, "  - Learning enabled: %s", config.enable_learning ? "Yes" : "No");
    ESP_LOGI(TAG, "  - Warmup time: %"PRIu32" ms", config.warmup_time_ms);
    ESP_LOGI(TAG, "Starting in WARMUP stage...");
}

// Calculate simple XOR checksum
static uint8_t calculate_checksum(const uint8_t* data, size_t length)
{
    uint8_t checksum = 0;
    for (size_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

// Process thermal anomaly detection with learning support
static void process_thermal_anomaly_detection(void)
{
    // Get temperature values from MLX90640 (1D array format)
    float* temp_values = mlx90640_get_temp_values();
    if (temp_values == NULL) {
        ESP_LOGE(TAG, "Failed to get temperature values for anomaly detection");
        return;
    }

    // Process frame using learning-enabled 1D array interface with timing measurement
    thermal_result_t result;

    // Start timing measurement
    int64_t start_time = esp_timer_get_time();

    int ret = thermal_detector_process_frame_1d_with_learning(&thermal_detector, temp_values, &result);

    // End timing measurement and calculate duration
    int64_t end_time = esp_timer_get_time();
    int64_t execution_time_us = end_time - start_time;

    // Log execution time in a clear, readable format
    if (execution_time_us >= 1000) {
        // Display in milliseconds if >= 1ms
        ESP_LOGI(TAG, "execution time: %.3f ms",
                execution_time_us / 1000.0);
    } else {
        // Display in microseconds if < 1ms
        ESP_LOGI(TAG, "execution time: %lld μs",
                execution_time_us);
    }

    if (ret != 0) {
        ESP_LOGE(TAG, "Failed to process thermal frame: %d", ret);
        return;
    }

    // Handle state transitions and learning progress
    static thermal_detector_state_t last_state = STATE_WARMUP;
    static uint32_t last_progress_log = 0;

    if (result.current_state != last_state) {
        // Log state transitions
        const char* state_names[] = {"WARMUP", "LEARNING", "JUDGING"};
        ESP_LOGI(TAG, "State transition: %s -> %s at frame %"PRIu32,
                state_names[last_state], state_names[result.current_state], result.frame_count);
        last_state = result.current_state;

        if (result.current_state == STATE_JUDGING && result.learning_completed) {
            ESP_LOGI(TAG, "*** LEARNING COMPLETED! *** Generated threshold matrix from %d frames",
                    result.collected_frames);
        }
    }

    // Log learning progress every 100 frames during learning stage
    if (result.current_state == STATE_LEARNING && (result.frame_count - last_progress_log) >= 100) {
        float progress = thermal_detector_get_learning_progress(&thermal_detector);
        ESP_LOGI(TAG, "Learning progress: %.1f%% (%d/%d frames collected, %d consecutive problems)",
                progress * 100.0f, result.collected_frames, MAX_COLLECT, result.consecutive_problem_frames);
        last_progress_log = result.frame_count;
    }

    // Log detection results based on current state
    if (result.current_state == STATE_WARMUP) {
        ESP_LOGD(TAG, "Frame %"PRIu32": WARMUP stage", result.frame_count);
    } else if (result.current_state == STATE_LEARNING) {
        ESP_LOGD(TAG, "Frame %"PRIu32": LEARNING stage (collected: %d)",
                result.frame_count, result.collected_frames);
    } else if (result.current_state == STATE_JUDGING) {
        // Normal judging stage logging
        if (result.is_filtered) {
            ESP_LOGD(TAG, "Frame %"PRIu32": FILTERED", result.frame_count);
        } else if (result.anomaly_detected) {
            ESP_LOGI(TAG, "Frame %"PRIu32" (%s): ANOMALY at (%.1f, %.1f), %d pixels",
                    result.frame_count,
                    result.is_even_frame ? "even" : "odd",
                    result.anomaly_center.x, result.anomaly_center.y,
                    (int)result.anomaly_pixel_count);

            if (result.trigger_anomaly) {
                ESP_LOGW(TAG, "*** THERMAL ANOMALY DETECTED! *** Distance: %.1f pixels",
                        result.center_distance);
            }
        } else {
            ESP_LOGD(TAG, "Frame %"PRIu32" (%s): Normal (%d pixels updated)",
                    result.frame_count,
                    result.is_even_frame ? "even" : "odd",
                    (int)result.updated_pixels);
        }
    }

    // Transmit result data
    transmit_thermal_result(&result);
}

// Transmit thermal anomaly detection result with learning state information
static void transmit_thermal_result(const thermal_result_t* result)
{
    // Define result frame structure with learning state information
    typedef struct __attribute__((packed)) {
        uint8_t sync1;                  // 0xAA
        uint8_t sync2;                  // 0x55
        uint16_t payload_length;        // Length of payload
        uint32_t frame_count;           // Frame number
        uint8_t flags;                  // Bit flags: [7:filtered, 6:anomaly_detected, 5:trigger_anomaly, 4:is_even_frame, 3-0:reserved]
        uint8_t current_state;          // Current detector state (0:WARMUP, 1:LEARNING, 2:JUDGING)
        uint16_t collected_frames;      // Number of frames collected in learning stage
        uint16_t consecutive_problems;  // Consecutive problem frames count
        uint8_t learning_completed;     // Learning completion flag (0:false, 1:true)
        float anomaly_center_x;         // Anomaly center X coordinate
        float anomaly_center_y;         // Anomaly center Y coordinate
        uint16_t anomaly_pixel_count;   // Number of anomaly pixels
        uint16_t updated_pixels;        // Number of updated pixels
        float center_distance;          // Distance between even/odd centers
        uint8_t checksum;               // XOR checksum
    } thermal_result_frame_t;

    static thermal_result_frame_t frame;

    // Build frame
    frame.sync1 = SYNC_BYTE_1;
    frame.sync2 = SYNC_BYTE_2;
    frame.payload_length = sizeof(thermal_result_frame_t) - 4; // Exclude sync bytes and length
    frame.frame_count = result->frame_count;

    // Pack flags
    frame.flags = 0;
    if (result->is_filtered) frame.flags |= 0x80;
    if (result->anomaly_detected) frame.flags |= 0x40;
    if (result->trigger_anomaly) frame.flags |= 0x20;
    if (result->is_even_frame) frame.flags |= 0x10;

    // Copy learning state information
    frame.current_state = (uint8_t)result->current_state;
    frame.collected_frames = (uint16_t)result->collected_frames;
    frame.consecutive_problems = (uint16_t)result->consecutive_problem_frames;
    frame.learning_completed = result->learning_completed ? 1 : 0;

    // Copy result data
    frame.anomaly_center_x = result->anomaly_center.valid ? result->anomaly_center.x : -1.0f;
    frame.anomaly_center_y = result->anomaly_center.valid ? result->anomaly_center.y : -1.0f;
    frame.anomaly_pixel_count = result->anomaly_pixel_count;
    frame.updated_pixels = result->updated_pixels;
    frame.center_distance = result->center_distance;

    // Calculate checksum (exclude sync bytes, length, and checksum field)
    uint8_t* payload = (uint8_t*)&frame.frame_count;
    size_t payload_size = sizeof(thermal_result_frame_t) - 5; // Exclude sync, length, and checksum
    frame.checksum = calculate_checksum(payload, payload_size);

    // Transmit frame
    int bytes_written = uart_write_bytes(UART_PORT_NUM, (uint8_t*)&frame, sizeof(frame));
    if (bytes_written == sizeof(frame)) {
        ESP_LOGD(TAG, "Thermal result frame transmitted (%zu bytes)", sizeof(frame));
    } else {
        ESP_LOGE(TAG, "UART transmission failed: %d bytes written", bytes_written);
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "Starting MLX90640 Thermal Anomaly Detection System");

    /* Initialize UART for data transmission */
    ESP_LOGI(TAG, "Initializing UART...");
    uart_init();
    vTaskDelay(50 / portTICK_PERIOD_MS);

    /* Initialize MLX90640 I2C communication (using preferred GPIO pins 3 and 8) */
    ESP_LOGI(TAG, "Initializing MLX90640 I2C...");
    MLX90640_I2CInit();
    vTaskDelay(50 / portTICK_PERIOD_MS);

    /* Initialize MLX90640 display functionality */
    ESP_LOGI(TAG, "Initializing MLX90640 display...");
    mlx90640_display_init();
    vTaskDelay(50 / portTICK_PERIOD_MS);

    /* Initialize thermal anomaly detection system */
    ESP_LOGI(TAG, "Initializing thermal anomaly detector...");
    thermal_detector_init_esp32();
    vTaskDelay(50 / portTICK_PERIOD_MS);

    ESP_LOGI(TAG, "System initialization complete. Starting thermal anomaly detection...");

    while (1) {
        /* Process MLX90640 temperature data */
        mlx90640_display_process();

        /* Process thermal anomaly detection */
        process_thermal_anomaly_detection();

        /* Delay between measurements to control frame rate */
        vTaskDelay(100 / portTICK_PERIOD_MS);  // ~10 FPS
    }
}
