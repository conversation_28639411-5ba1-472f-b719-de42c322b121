# MLX90640 Thermal Imaging System

A complete thermal imaging data transmission and visualization system for ESP32 with MLX90640 thermal sensor.

## System Overview

This system consists of two main components:

1. **ESP32 Data Transmission (C/ESP-IDF)**: Reads thermal data from MLX90640 sensor and transmits it via UART
2. **Python Host Application**: Receives data and provides real-time thermal imaging visualization

## Hardware Requirements

- ESP32 development board (ESP32-S3 recommended)
- MLX90640 thermal imaging sensor
- USB cable for ESP32 connection
- Jumper wires for I2C connections

## Wiring

Connect MLX90640 to ESP32:
- MLX90640 VCC → ESP32 3.3V
- MLX90640 GND → ESP32 GND  
- MLX90640 SDA → ESP32 GPIO 3
- MLX90640 SCL → ESP32 GPIO 8

## ESP32 Firmware

### Features
- I2C communication with MLX90640 on GPIO pins 3 (SDA) and 8 (SCL)
- Structured data frame protocol for reliable transmission
- Real-time temperature data acquisition at ~10 FPS
- UART transmission at 115200 baud

### Data Frame Format
```
[0xAA][0x55][LENGTH_H][LENGTH_L][PAYLOAD][CHECKSUM]

Where:
- Sync bytes: 0xAA, 0x55
- Length: 16-bit payload size (3072 bytes)
- Payload: 768 float values (32x24 temperature array)
- Checksum: XOR checksum of payload
- Total frame size: 3077 bytes
```

### Building and Flashing

1. Ensure ESP-IDF is installed and configured
2. Navigate to the project directory
3. Build and flash:
```bash
idf.py build
idf.py flash monitor
```

## Python Host Application

### Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

### Usage

Run the thermal visualizer:
```bash
python thermal_visualizer.py COM3  # Windows
python thermal_visualizer.py /dev/ttyUSB0  # Linux
```

Options:
- `--baudrate`: Set baud rate (default: 115200)
- `--verbose`: Enable verbose logging
- `-h`: Show help

### Features
- Real-time thermal imaging display
- Auto-scaling color mapping
- Temperature statistics (min/max/average)
- Frame counter and error tracking
- Custom thermal colormap
- Bilinear interpolation for smooth visualization

## System Performance

- **Frame Rate**: ~10 FPS
- **Resolution**: 32x24 pixels (768 temperature points)
- **Temperature Range**: Typically -40°C to +300°C (sensor dependent)
- **Data Rate**: ~30KB/s at 10 FPS

## Troubleshooting

### ESP32 Issues
- Check I2C wiring connections
- Verify MLX90640 power supply (3.3V)
- Monitor serial output for error messages
- Ensure correct GPIO pin configuration

### Python Application Issues
- Verify correct serial port name
- Check USB cable connection
- Install required Python packages
- Try different baud rates if communication fails

### Common Problems
1. **No data received**: Check serial port and baud rate
2. **Checksum errors**: Verify stable power supply and connections
3. **Frame sync issues**: Restart both ESP32 and Python application
4. **Temperature readings seem wrong**: Check sensor orientation and calibration

## Development Notes

### ESP32 Code Structure
- `main/blink_example_main.c`: Main application with UART transmission
- `mlx90640/`: MLX90640 library files
- `mlx90640/mlx90640_lcd_display.c`: Temperature data processing
- `mlx90640/MLX90640_I2C_Driver.cpp`: I2C communication driver

### Python Code Structure
- `thermal_visualizer.py`: Main visualization application
- Serial communication with frame synchronization
- Real-time matplotlib animation
- Error handling and statistics

## License

This project combines code from multiple sources:
- MLX90640 API: Melexis N.V. (Apache License 2.0)
- ESP32 components: Espressif Systems
- Custom integration code: Public Domain

## Support

For issues and questions:
1. Check ESP32 serial monitor output
2. Enable verbose logging in Python application
3. Verify hardware connections
4. Check system requirements
