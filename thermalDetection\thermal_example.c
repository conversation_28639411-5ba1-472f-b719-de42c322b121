/**
 * @file thermal_example.c
 * @brief Example usage of the thermal anomaly detection core algorithm
 * 
 * This example demonstrates how to use the core thermal anomaly detection
 * algorithm with 32x24 float arrays.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */

#include "thermal_anomaly_core.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// Function to generate synthetic thermal data for testing
void generate_synthetic_thermal_frame(float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH], 
                                     int frame_number, bool add_anomaly)
{
    static float base_temp = 25.0f;
    
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            // Create a synthetic thermal pattern
            float x_factor = (float)j / (THERMAL_WIDTH - 1) - 0.5f;
            float y_factor = (float)i / (THERMAL_HEIGHT - 1) - 0.5f;
            float distance = sqrtf(x_factor * x_factor + y_factor * y_factor);
            
            // Add some noise and variation
            float noise = (float)(rand() % 100 - 50) / 1000.0f; // ±0.05°C noise
            float temp_variation = sinf(frame_number * 0.1f + distance * 10.0f) * 0.5f;
            
            thermal_frame[i][j] = base_temp + temp_variation + noise;
            
            // Add a "hot spot" to simulate a bird if requested
            if (add_anomaly && i >= 10 && i <= 14 && j >= 14 && j <= 18) {
                thermal_frame[i][j] += 5.0f; // +5°C hot spot
            }
        }
    }
}

// Function to print detection results
void print_detection_result(const thermal_result_t* result)
{
    printf("Frame %u (%s): ", result->frame_count, 
           result->is_even_frame ? "even" : "odd");
    
    if (result->is_filtered) {
        printf("FILTERED\n");
        return;
    }
    
    if (result->anomaly_detected) {
        printf("ANOMALY at (%.1f, %.1f), %u pixels", 
               result->anomaly_center.x, result->anomaly_center.y, 
               result->anomaly_pixel_count);
        
        if (result->trigger_anomaly) {
            printf(" -> TRIGGER! (distance: %.1f)", result->center_distance);
        }
        printf("\n");
    } else {
        printf("Normal\n");
    }
}

// Function to load personalized thresholds (example implementation)
int load_personalized_thresholds(float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH], 
                                const char* filename)
{
    // This is a placeholder implementation
    // In a real application, you would load thresholds from a file
    // that was generated by the Python training process
    
    printf("Loading personalized thresholds from %s...\n", filename);
    
    // For this example, create some varied thresholds
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            // Create some variation in thresholds (0.03 to 0.15)
            float base_threshold = 0.07f;
            float variation = (float)(rand() % 100) / 1000.0f - 0.05f; // ±0.05
            thresholds[i][j] = base_threshold + variation;
            
            // Ensure positive values
            if (thresholds[i][j] < 0.01f) {
                thresholds[i][j] = 0.01f;
            }
        }
    }
    
    printf("Loaded personalized thresholds (range: 0.01 - 0.15)\n");
    return 0; // Success
}

// int main(void)
// {
//     printf("=== Thermal Anomaly Detection Core Algorithm Example ===\n\n");
    
//     // Initialize detector
//     thermal_detector_t detector;
//     thermal_config_t config;
//     float personalized_thresholds[THERMAL_HEIGHT][THERMAL_WIDTH];
    
//     // Create configuration
//     thermal_detector_create_default_config(&config);
//     config.enable_pre_filter = true;
//     config.min_anomaly_pixels = 3;
//     config.center_distance_threshold = 20.0f;
    
//     printf("Configuration:\n");
//     printf("  Pre-filter: %s (%.1f - %.1f°C, max delta: %.1f°C)\n",
//            config.enable_pre_filter ? "enabled" : "disabled",
//            config.pre_filter_min, config.pre_filter_max, config.pre_filter_delta);
//     printf("  Window size: %u frames\n", config.window_size);
//     printf("  Min anomaly pixels: %u\n", config.min_anomaly_pixels);
//     printf("  Center distance threshold: %.1f pixels\n", config.center_distance_threshold);
//     printf("\n");
    
//     // Load personalized thresholds
//     if (load_personalized_thresholds(personalized_thresholds, "pixel_thresholds.dat") != 0) {
//         printf("Failed to load personalized thresholds, using defaults\n");
//         thermal_detector_create_default_thresholds(personalized_thresholds, 0.07f);
//     }
    
//     // Initialize detector
//     if (thermal_detector_init(&detector, &config, personalized_thresholds) != 0) {
//         printf("Failed to initialize thermal detector\n");
//         return -1;
//     }
    
//     printf("Thermal detector initialized successfully\n\n");
    
//     // Process thermal frames
//     printf("Processing thermal frames...\n");
//     printf("(Anomaly will be introduced at frame 50)\n\n");
    
//     for (int frame = 1; frame <= 100; frame++) {
//         float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH];
//         thermal_result_t result;
        
//         // Generate synthetic thermal data
//         bool add_anomaly = (frame >= 50 && frame <= 60);
//         generate_synthetic_thermal_frame(thermal_frame, frame, add_anomaly);
        
//         // Process frame
//         int ret = thermal_detector_process_frame(&detector, thermal_frame, &result);
//         if (ret != 0) {
//             printf("Error processing frame %d\n", frame);
//             continue;
//         }
        
//         // Print results for interesting frames
//         if (result.anomaly_detected || result.trigger_anomaly || (frame % 20 == 0)) {
//             print_detection_result(&result);
//         }
        
//         // Show trigger events prominently
//         if (result.trigger_anomaly) {
//             printf("*** BIRD DETECTED! ***\n");
//         }
//     }
    
//     printf("\nProcessing complete!\n");
//     printf("\nExample usage:\n");
//     printf("1. Initialize detector with thermal_detector_init()\n");
//     printf("2. For each thermal frame (32x24 float array):\n");
//     printf("   - Call thermal_detector_process_frame()\n");
//     printf("   - Check result.trigger_anomaly for bird detection\n");
//     printf("3. Use thermal_detector_reset() to reset state if needed\n");
    
//     return 0;
// }

// Compile with: gcc -o thermal_example thermal_example.c thermal_anomaly_core.c -lm
