/**
 * @file mlx90640_i2c_multi.c
 * @brief Extended I2C driver implementation for multiple MLX90640 sensors
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include "mlx90640_i2c_multi.h"
#include "mlx90640_software_i2c.h"
#include "MLX90640_API.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "MLX90640_MULTI_I2C";

// Global sensor configuration table for 4 independent sensors
const sensor_config_t sensor_configs[SENSOR_COUNT] = {
    {SENSOR_0, I2C_BUS_HARDWARE_0, MLX90640_ADDR_UNIFIED, "Sensor_0"},
    {SENSOR_1, I2C_BUS_HARDWARE_1, MLX90640_ADDR_UNIFIED, "Sensor_1"},
    {SENSOR_2, I2C_BUS_SOFTWARE_2, MLX90640_ADDR_UNIFIED, "Sensor_2"},
    {SENSOR_3, I2C_BUS_SOFTWARE_3, MLX90640_ADDR_UNIFIED, "Sensor_3"}
};

// ============================================================================
// I2C Multi-Bus Functions
// ============================================================================

esp_err_t mlx90640_i2c_multi_init(void)
{
    esp_err_t ret;

    MULTI_SENSOR_LOGI(TAG, "Initializing 4 I2C buses (2 hardware + 2 software) for multi-sensor operation");

    // Initialize Hardware I2C Bus 0 (I2C_NUM_0)
    i2c_config_t i2c_conf_0 = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = I2C_HW0_SDA_GPIO,
        .scl_io_num = I2C_HW0_SCL_GPIO,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
        .clk_flags = 0
    };

    ret = i2c_param_config(I2C_NUM_0, &i2c_conf_0);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Hardware I2C Bus 0 param config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = i2c_driver_install(I2C_NUM_0, I2C_MODE_MASTER, 0, 0, 0);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Hardware I2C Bus 0 driver install failed: %s", esp_err_to_name(ret));
        return ret;
    }

    MULTI_SENSOR_LOGI(TAG, "Hardware I2C Bus 0 initialized (SDA:%d, SCL:%d)", I2C_HW0_SDA_GPIO, I2C_HW0_SCL_GPIO);
    
    // Initialize Hardware I2C Bus 1 (I2C_NUM_1)
    i2c_config_t i2c_conf_1 = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = I2C_HW1_SDA_GPIO,
        .scl_io_num = I2C_HW1_SCL_GPIO,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
        .clk_flags = 0
    };

    ret = i2c_param_config(I2C_NUM_1, &i2c_conf_1);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Hardware I2C Bus 1 param config failed: %s", esp_err_to_name(ret));
        i2c_driver_delete(I2C_NUM_0);  // Cleanup Bus 0
        return ret;
    }

    ret = i2c_driver_install(I2C_NUM_1, I2C_MODE_MASTER, 0, 0, 0);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Hardware I2C Bus 1 driver install failed: %s", esp_err_to_name(ret));
        i2c_driver_delete(I2C_NUM_0);  // Cleanup Bus 0
        return ret;
    }

    MULTI_SENSOR_LOGI(TAG, "Hardware I2C Bus 1 initialized (SDA:%d, SCL:%d)", I2C_HW1_SDA_GPIO, I2C_HW1_SCL_GPIO);

    // Initialize Software I2C Bus 2
    ret = sw_i2c_init(I2C_BUS_SOFTWARE_2);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Software I2C Bus 2 initialization failed: %s", esp_err_to_name(ret));
        i2c_driver_delete(I2C_NUM_1);
        i2c_driver_delete(I2C_NUM_0);
        return ret;
    }

    // Initialize Software I2C Bus 3
    ret = sw_i2c_init(I2C_BUS_SOFTWARE_3);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Software I2C Bus 3 initialization failed: %s", esp_err_to_name(ret));
        sw_i2c_deinit(I2C_BUS_SOFTWARE_2);
        i2c_driver_delete(I2C_NUM_1);
        i2c_driver_delete(I2C_NUM_0);
        return ret;
    }

    MULTI_SENSOR_LOGI(TAG, "All 4 I2C buses initialized successfully (2 hardware + 2 software)");
    
    return ESP_OK;
}

esp_err_t mlx90640_i2c_multi_deinit(void)
{
    esp_err_t ret0 = i2c_driver_delete(I2C_NUM_0);
    esp_err_t ret1 = i2c_driver_delete(I2C_NUM_1);
    esp_err_t ret2 = sw_i2c_deinit(I2C_BUS_SOFTWARE_2);
    esp_err_t ret3 = sw_i2c_deinit(I2C_BUS_SOFTWARE_3);

    if (ret0 != ESP_OK || ret1 != ESP_OK || ret2 != ESP_OK || ret3 != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "I2C deinit failed: HW0=%s, HW1=%s, SW2=%s, SW3=%s",
                         esp_err_to_name(ret0), esp_err_to_name(ret1),
                         esp_err_to_name(ret2), esp_err_to_name(ret3));
        return ESP_FAIL;
    }

    MULTI_SENSOR_LOGI(TAG, "All 4 I2C buses deinitialized successfully");
    return ESP_OK;
}

int mlx90640_i2c_multi_read(uint8_t i2c_port, uint8_t slave_addr, 
                           uint16_t start_address, uint16_t n_mem_address_read, 
                           uint16_t *data)
{
    if (i2c_port > 1) {
        MULTI_SENSOR_LOGE(TAG, "Invalid I2C port: %d", i2c_port);
        return -1;
    }
    
    uint8_t* bp = (uint8_t*) data;
    uint8_t write_buf[2];
    esp_err_t ret;
    
    // Prepare the register address (16-bit, big-endian)
    write_buf[0] = (start_address >> 8) & 0xFF;
    write_buf[1] = start_address & 0xFF;
    
    // Perform I2C write-read transaction
    ret = i2c_master_write_read_device((i2c_port_t)i2c_port, slave_addr, 
                                       write_buf, sizeof(write_buf),
                                       bp, n_mem_address_read * 2, 
                                       I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "I2C read failed on port %d, addr 0x%02X: %s", 
                         i2c_port, slave_addr, esp_err_to_name(ret));
        return -1;
    }
    
    // Swap bytes for each 16-bit word (MLX90640 sends MSB first)
    for(int cnt = 0; cnt < n_mem_address_read * 2; cnt += 2) {
        uint8_t tmpbytelsb = bp[cnt + 1];
        bp[cnt + 1] = bp[cnt];
        bp[cnt] = tmpbytelsb;
    }
    
    return 0;
}

int mlx90640_i2c_multi_write(uint8_t i2c_port, uint8_t slave_addr,
                            uint16_t write_address, uint16_t data)
{
    if (i2c_port > 1) {
        MULTI_SENSOR_LOGE(TAG, "Invalid I2C port: %d", i2c_port);
        return -1;
    }
    
    uint8_t write_buf[4];
    esp_err_t ret;
    uint16_t dataCheck;
    
    // Prepare the write buffer: [reg_addr_high, reg_addr_low, data_high, data_low]
    write_buf[0] = (write_address >> 8) & 0xFF;
    write_buf[1] = write_address & 0xFF;
    write_buf[2] = (data >> 8) & 0xFF;
    write_buf[3] = data & 0xFF;
    
    // Perform I2C write transaction
    ret = i2c_master_write_to_device((i2c_port_t)i2c_port, slave_addr, 
                                     write_buf, sizeof(write_buf),
                                     I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "I2C write failed on port %d, addr 0x%02X: %s", 
                         i2c_port, slave_addr, esp_err_to_name(ret));
        return -1;
    }
    
    // Verify the write by reading back the data (except for special addresses)
    if (write_address != 0x8000) {
        if (mlx90640_i2c_multi_read(i2c_port, slave_addr, write_address, 1, &dataCheck) != 0) {
            MULTI_SENSOR_LOGE(TAG, "I2C write verification read failed");
            return -1;
        }
        
        if (dataCheck != data) {
            MULTI_SENSOR_LOGE(TAG, "I2C write verification failed: expected 0x%04X, got 0x%04X", 
                             data, dataCheck);
            return -2;
        }
    }
    
    return 0;
}

// ============================================================================
// Sensor Management Functions
// ============================================================================

bool mlx90640_sensor_detect(uint8_t i2c_port, uint8_t slave_addr)
{
    uint16_t test_data;
    
    // Try to read device ID or any register to check if sensor responds
    int result = mlx90640_i2c_multi_read(i2c_port, slave_addr, 0x2407, 1, &test_data);
    
    if (result == 0) {
        MULTI_SENSOR_LOGI(TAG, "Sensor detected on I2C port %d, address 0x%02X", 
                         i2c_port, slave_addr);
        return true;
    } else {
        MULTI_SENSOR_LOGD(TAG, "No sensor found on I2C port %d, address 0x%02X", 
                         i2c_port, slave_addr);
        return false;
    }
}

esp_err_t mlx90640_change_sensor_address(uint8_t i2c_port, uint8_t old_addr, uint8_t new_addr)
{
    MULTI_SENSOR_LOGI(TAG, "Changing sensor address on I2C port %d: 0x%02X -> 0x%02X", 
                     i2c_port, old_addr, new_addr);
    
    // Check if sensor exists at old address
    if (!mlx90640_sensor_detect(i2c_port, old_addr)) {
        MULTI_SENSOR_LOGE(TAG, "No sensor found at old address 0x%02X", old_addr);
        return ESP_ERR_NOT_FOUND;
    }
    
    // Write new address to the sensor's address register (0x2407)
    int result = mlx90640_i2c_multi_write(i2c_port, old_addr, 0x2407, new_addr);
    if (result != 0) {
        MULTI_SENSOR_LOGE(TAG, "Failed to write new address to sensor");
        return ESP_FAIL;
    }
    
    // Wait for address change to take effect
    vTaskDelay(ADDRESS_CHANGE_DELAY_MS / portTICK_PERIOD_MS);
    
    // Verify sensor responds at new address
    if (!mlx90640_sensor_detect(i2c_port, new_addr)) {
        MULTI_SENSOR_LOGE(TAG, "Sensor not responding at new address 0x%02X", new_addr);
        return ESP_FAIL;
    }
    
    // Verify sensor no longer responds at old address
    if (mlx90640_sensor_detect(i2c_port, old_addr)) {
        MULTI_SENSOR_LOGW(TAG, "Sensor still responds at old address 0x%02X", old_addr);
    }
    
    MULTI_SENSOR_LOGI(TAG, "Sensor address changed successfully: 0x%02X -> 0x%02X", 
                     old_addr, new_addr);
    return ESP_OK;
}

esp_err_t mlx90640_init_sensor(uint8_t i2c_port, uint8_t slave_addr)
{
    MULTI_SENSOR_LOGI(TAG, "Initializing sensor on I2C port %d, address 0x%02X", 
                     i2c_port, slave_addr);
    
    // Check if sensor is present
    if (!mlx90640_sensor_detect(i2c_port, slave_addr)) {
        MULTI_SENSOR_LOGE(TAG, "Sensor not found for initialization");
        return ESP_ERR_NOT_FOUND;
    }
    
    // TODO: Add MLX90640-specific initialization sequence here
    // This would include reading EEPROM, setting refresh rate, etc.
    // For now, we just verify the sensor is responding
    
    MULTI_SENSOR_LOGI(TAG, "Sensor initialized successfully");
    return ESP_OK;
}

// ============================================================================
// GPIO Control Functions (Simplified for 4-sensor independent I2C architecture)
// ============================================================================

esp_err_t mlx90640_gpio_init(void)
{
    MULTI_SENSOR_LOGI(TAG, "GPIO initialization for 4-sensor independent I2C architecture");
    // No GPIO switching needed - each sensor has dedicated I2C bus
    return ESP_OK;
}

// Deprecated function - kept for compatibility but marked as deprecated
esp_err_t __attribute__((deprecated)) mlx90640_switch_control(gpio_num_t switch_gpio, bool enable)
{
    MULTI_SENSOR_LOGW(TAG, "mlx90640_switch_control is deprecated in 4-sensor architecture");
    return ESP_OK;
}

esp_err_t mlx90640_sensor_power_control(sensor_id_t sensor_id, bool enable)
{
    // In 4-sensor independent I2C architecture, all sensors are always powered
    // No power control switching needed
    MULTI_SENSOR_LOGD(TAG, "Sensor %s power control: %s (no-op in 4-sensor architecture)",
                     mlx90640_get_sensor_name(sensor_id), enable ? "ON" : "OFF");
    return ESP_OK;
}

// ============================================================================
// Utility Functions
// ============================================================================

const char* mlx90640_get_i2c_bus_name(i2c_bus_type_t bus_type)
{
    switch (bus_type) {
        case I2C_BUS_HARDWARE_0: return "HW_I2C_0";
        case I2C_BUS_HARDWARE_1: return "HW_I2C_1";
        case I2C_BUS_SOFTWARE_2: return "SW_I2C_2";
        case I2C_BUS_SOFTWARE_3: return "SW_I2C_3";
        default: return "UNKNOWN";
    }
}

const char* mlx90640_get_sensor_name(sensor_id_t sensor_id)
{
    if (sensor_id < SENSOR_COUNT) {
        return sensor_configs[sensor_id].name;
    }
    return "UNKNOWN";
}

esp_err_t mlx90640_sensor_to_i2c_params(sensor_id_t sensor_id, i2c_bus_type_t* bus_type, uint8_t* slave_addr)
{
    if (sensor_id >= SENSOR_COUNT || bus_type == NULL || slave_addr == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    *bus_type = sensor_configs[sensor_id].bus_type;
    *slave_addr = sensor_configs[sensor_id].device_addr;

    return ESP_OK;
}

esp_err_t mlx90640_read_temperature_multi(sensor_id_t sensor_id, float* temp_data)
{
    if (sensor_id >= SENSOR_COUNT || temp_data == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    i2c_bus_type_t bus_type;
    uint8_t slave_addr;
    esp_err_t ret = mlx90640_sensor_to_i2c_params(sensor_id, &bus_type, &slave_addr);
    if (ret != ESP_OK) {
        return ret;
    }
    
    // Read frame data from MLX90640
    uint16_t frame_data[834];
    int mlx_ret = MLX90640_GetFrameData(slave_addr, frame_data);
    if (mlx_ret != 0) {
        MULTI_SENSOR_LOGE(TAG, "Failed to get frame data from sensor %s: %d",
                         mlx90640_get_sensor_name(sensor_id), mlx_ret);
        return ESP_FAIL;
    }

    // Get MLX90640 parameters (should be cached from initialization)
    paramsMLX90640 mlx_params;
    mlx_ret = MLX90640_ExtractParameters(frame_data, &mlx_params);
    if (mlx_ret != 0) {
        MULTI_SENSOR_LOGE(TAG, "Failed to extract parameters from sensor %s: %d",
                         mlx90640_get_sensor_name(sensor_id), mlx_ret);
        return ESP_FAIL;
    }

    // Calculate temperatures
    float emissivity = 0.95f;  // Default emissivity
    float tr = 23.15f;         // Reflected temperature (room temp in Celsius + 273.15)
    MLX90640_CalculateTo(frame_data, &mlx_params, emissivity, tr, temp_data);

    MULTI_SENSOR_LOGD(TAG, "Temperature read from sensor %s completed",
                     mlx90640_get_sensor_name(sensor_id));

    return ESP_OK;
}

// ============================================================================
// Unified I2C Interface Functions (Hardware + Software)
// ============================================================================

int mlx90640_i2c_unified_read(i2c_bus_type_t bus_type, uint8_t slave_addr,
                              uint16_t start_address, uint16_t n_mem_address_read,
                              uint16_t *data)
{
    if (bus_type >= I2C_BUS_COUNT || !data || n_mem_address_read == 0) {
        MULTI_SENSOR_LOGE(TAG, "Invalid parameters for unified I2C read");
        return -1;
    }

    esp_err_t ret;

    // Use hardware I2C for buses 0 and 1
    if (bus_type <= I2C_BUS_HARDWARE_1) {
        uint8_t* bp = (uint8_t*) data;
        uint8_t write_buf[2];

        // Prepare the register address (16-bit, big-endian)
        write_buf[0] = (start_address >> 8) & 0xFF;
        write_buf[1] = start_address & 0xFF;

        // Perform I2C write-read transaction
        ret = i2c_master_write_read_device((i2c_port_t)bus_type, slave_addr,
                                           write_buf, sizeof(write_buf),
                                           bp, n_mem_address_read * 2,
                                           I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);

        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Hardware I2C read failed on bus %d, addr 0x%02X: %s",
                             bus_type, slave_addr, esp_err_to_name(ret));
            return -1;
        }

        // Swap bytes for each 16-bit word (MLX90640 sends MSB first)
        for(int cnt = 0; cnt < n_mem_address_read * 2; cnt += 2) {
            uint8_t tmpbytelsb = bp[cnt + 1];
            bp[cnt + 1] = bp[cnt];
            bp[cnt] = tmpbytelsb;
        }
    }
    // Use software I2C for buses 2 and 3
    else {
        uint8_t* bp = (uint8_t*) data;

        // Read data using software I2C
        ret = sw_i2c_read(bus_type, slave_addr, start_address, bp, n_mem_address_read * 2);

        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Software I2C read failed on bus %d, addr 0x%02X: %s",
                             bus_type, slave_addr, esp_err_to_name(ret));
            return -1;
        }

        // Swap bytes for each 16-bit word (MLX90640 sends MSB first)
        for(int cnt = 0; cnt < n_mem_address_read * 2; cnt += 2) {
            uint8_t tmpbytelsb = bp[cnt + 1];
            bp[cnt + 1] = bp[cnt];
            bp[cnt] = tmpbytelsb;
        }
    }

    return 0;
}

int mlx90640_i2c_unified_write(i2c_bus_type_t bus_type, uint8_t slave_addr,
                               uint16_t write_address, uint16_t data)
{
    if (bus_type >= I2C_BUS_COUNT) {
        MULTI_SENSOR_LOGE(TAG, "Invalid bus type for unified I2C write: %d", bus_type);
        return -1;
    }

    esp_err_t ret;

    // Use hardware I2C for buses 0 and 1
    if (bus_type <= I2C_BUS_HARDWARE_1) {
        uint8_t write_buf[4];

        // Prepare write buffer: [reg_addr_msb, reg_addr_lsb, data_msb, data_lsb]
        write_buf[0] = (write_address >> 8) & 0xFF;
        write_buf[1] = write_address & 0xFF;
        write_buf[2] = (data >> 8) & 0xFF;
        write_buf[3] = data & 0xFF;

        ret = i2c_master_write_to_device((i2c_port_t)bus_type, slave_addr,
                                         write_buf, sizeof(write_buf),
                                         I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);

        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Hardware I2C write failed on bus %d, addr 0x%02X: %s",
                             bus_type, slave_addr, esp_err_to_name(ret));
            return -1;
        }
    }
    // Use software I2C for buses 2 and 3
    else {
        uint8_t write_data[2];
        write_data[0] = (data >> 8) & 0xFF;
        write_data[1] = data & 0xFF;

        ret = sw_i2c_write(bus_type, slave_addr, write_address, write_data, 2);

        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Software I2C write failed on bus %d, addr 0x%02X: %s",
                             bus_type, slave_addr, esp_err_to_name(ret));
            return -1;
        }
    }

    return 0;
}

bool mlx90640_sensor_detect_unified(i2c_bus_type_t bus_type, uint8_t slave_addr)
{
    if (bus_type >= I2C_BUS_COUNT) {
        return false;
    }

    // Use hardware I2C for buses 0 and 1
    if (bus_type <= I2C_BUS_HARDWARE_1) {
        esp_err_t ret = i2c_master_write_to_device((i2c_port_t)bus_type, slave_addr,
                                                   NULL, 0,
                                                   I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
        return (ret == ESP_OK);
    }
    // Use software I2C for buses 2 and 3
    else {
        return sw_i2c_device_present(bus_type, slave_addr);
    }
}
