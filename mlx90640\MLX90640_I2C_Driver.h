/**
 * @copyright (C) 2017 Melexis N.V.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#ifndef _MLX90640_I2C_Driver_H_
#define _MLX90640_I2C_Driver_H_
#ifdef __cplusplus
 extern "C" {
#endif
#include <stdint.h>
#include <stdbool.h>
#include "../mlx90640_multi/mlx90640_multi_config.h"

// ============================================================================
// I2C Context Management for Unified Interface Integration
// ============================================================================

/**
 * @brief Set the I2C bus context for MLX90640 operations
 *
 * This function sets the I2C bus type that will be used for subsequent
 * MLX90640_I2CRead and MLX90640_I2CWrite operations. It enables the
 * MLX90640 library to work with the unified I2C interface supporting
 * both hardware and software I2C buses.
 *
 * @param bus_type I2C bus type (hardware or software)
 * @return true if context was set successfully, false otherwise
 */
bool MLX90640_SetI2CContext(i2c_bus_type_t bus_type);

/**
 * @brief Get the current I2C bus context
 *
 * @return Current I2C bus type, or I2C_BUS_HARDWARE_0 if not set
 */
i2c_bus_type_t MLX90640_GetI2CContext(void);

/**
 * @brief Initialize the unified I2C interface for MLX90640
 *
 * This function initializes all I2C buses through the unified interface
 * instead of the original single-bus initialization.
 *
 * @return true if initialization was successful, false otherwise
 */
bool MLX90640_I2CInit_Unified(void);

// ============================================================================
// Original MLX90640 I2C Functions (now context-aware)
// ============================================================================

void MLX90640_I2CInit(void);
int  MLX90640_I2CRead(uint8_t slaveAddr,uint16_t startAddress, uint16_t nMemAddressRead, uint16_t *data);
int  MLX90640_I2CWrite(uint8_t slaveAddr,uint16_t writeAddress, uint16_t data);
void MLX90640_I2CFreqSet(int freq);
#ifdef __cplusplus
}
#endif
#endif
    
 
 

