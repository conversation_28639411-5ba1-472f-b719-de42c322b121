/**
 * @file mlx90640_multi_config.h
 * @brief Configuration macros for 4-sensor MLX90640 system
 * 
 * This file contains all configuration macros for the multi-sensor system,
 * making it easy to modify GPIO pins, addresses, and task parameters.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MLX90640_MULTI_CONFIG_H
#define MLX90640_MULTI_CONFIG_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// GPIO Configuration - 4 Independent I2C Buses (2 Hardware + 2 Software)
// ============================================================================

// Hardware I2C Bus 0 Configuration (GPIO 21,22)
#define I2C_HW0_SDA_GPIO        21
#define I2C_HW0_SCL_GPIO        22

// Hardware I2C Bus 1 Configuration (GPIO 18,19)
#define I2C_HW1_SDA_GPIO        18
#define I2C_HW1_SCL_GPIO        19

// Software I2C Bus 2 Configuration (GPIO 4,5)
#define I2C_SW2_SDA_GPIO        4
#define I2C_SW2_SCL_GPIO        5

// Software I2C Bus 3 Configuration (GPIO 16,17)
#define I2C_SW3_SDA_GPIO        16
#define I2C_SW3_SCL_GPIO        17

// ============================================================================
// I2C Configuration - 4 Independent Buses
// ============================================================================

#define I2C_MASTER_FREQ_HZ      400000  // 400kHz I2C frequency for hardware I2C
#define I2C_SOFTWARE_FREQ_HZ    400000  // 400kHz for software I2C (same as hardware)
#define I2C_MASTER_TIMEOUT_MS   1000    // I2C timeout in milliseconds

// I2C Bus Types
typedef enum {
    I2C_BUS_HARDWARE_0 = 0,  // Hardware I2C Bus 0 (GPIO 21,22)
    I2C_BUS_HARDWARE_1 = 1,  // Hardware I2C Bus 1 (GPIO 18,19)
    I2C_BUS_SOFTWARE_2 = 2,  // Software I2C Bus 2 (GPIO 4,5)
    I2C_BUS_SOFTWARE_3 = 3,  // Software I2C Bus 3 (GPIO 16,17)
    I2C_BUS_COUNT = 4
} i2c_bus_type_t;

// ============================================================================
// Sensor Address Configuration - Unified Address
// ============================================================================

#define MLX90640_ADDR_UNIFIED   0x33  // All sensors use same address on different buses

// ============================================================================
// Sensor Identification - 4 Independent Sensors
// ============================================================================

typedef enum {
    SENSOR_0 = 0,    // Hardware I2C Bus 0, Address 0x33
    SENSOR_1 = 1,    // Hardware I2C Bus 1, Address 0x33
    SENSOR_2 = 2,    // Software I2C Bus 2, Address 0x33
    SENSOR_3 = 3,    // Software I2C Bus 3, Address 0x33
    SENSOR_COUNT = 4
} sensor_id_t;

// Sensor configuration lookup table for 4 independent sensors
typedef struct {
    sensor_id_t id;
    i2c_bus_type_t bus_type;    // I2C bus type (hardware or software)
    uint8_t device_addr;        // Always 0x33 for unified addressing
    const char* name;           // Human readable name
} sensor_config_t;

// ============================================================================
// Task Configuration - Optimized for 4-I2C Architecture
// ============================================================================

// Task Priorities (higher number = higher priority)
// Software I2C needs higher priority for precise timing
#define ACQUIRE_TASK_PRIORITY_HW    5     // Hardware I2C acquisition tasks (Core 0)
#define ACQUIRE_TASK_PRIORITY_SW    6     // Software I2C acquisition tasks (Core 0) - Higher priority
#define PROCESS_TASK_PRIORITY       3     // All processing tasks (Core 1)

// Task Stack Sizes - Optimized for I2C type
#define ACQUIRE_TASK_STACK_HW       8192  // 8KB for hardware I2C acquisition
#define ACQUIRE_TASK_STACK_SW       10240 // 10KB for software I2C acquisition (needs more stack)
#define PROCESS_TASK_STACK          16384 // 16KB for processing tasks

// Queue Configuration - Enhanced for 4-sensor system
#define DATA_QUEUE_LENGTH           3     // Increased queue depth for better buffering
#define DATA_QUEUE_ITEM_SIZE        sizeof(uint32_t)

// Frame Rate Control - Staggered timing for load balancing
#define FRAME_INTERVAL_MS           100   // ~10 FPS per sensor
#define FRAME_STAGGER_MS            25    // Stagger start times to balance load

// ============================================================================
// System Configuration
// ============================================================================

// Initialization Delays
#define GPIO_INIT_DELAY_MS      50
#define I2C_INIT_DELAY_MS       50
#define SENSOR_INIT_DELAY_MS    100
#define ADDRESS_CHANGE_DELAY_MS 50

// Error Handling - Enhanced for 4-I2C system
#define MAX_INIT_RETRIES        3
#define MAX_ERROR_COUNT         10
#define MAX_SW_I2C_ERRORS       5     // Lower threshold for software I2C
#define ERROR_RECOVERY_DELAY_MS 1000  // Delay before task restart

// Memory Configuration
#ifndef THERMAL_PIXELS
#define THERMAL_PIXELS          768   // 24 * 32
#endif
#define TEMP_DATA_SIZE          (THERMAL_PIXELS * sizeof(float))

// ============================================================================
// Task Synchronization and Performance Configuration
// ============================================================================

// Task synchronization timeouts
#define TASK_SYNC_TIMEOUT_MS        100   // Timeout for task synchronization
#define DATA_READY_TIMEOUT_MS       200   // Timeout for data ready notifications
#define MUTEX_TIMEOUT_MS            50    // Timeout for mutex acquisition

// Performance monitoring intervals
#define PERF_MONITOR_INTERVAL_MS    5000  // Performance monitoring interval
#define HEALTH_CHECK_INTERVAL_MS    1000  // Task health check interval

// Software I2C specific configuration
#define SW_I2C_RETRY_COUNT          3     // Retry count for software I2C operations
#define SW_I2C_DELAY_US             10    // Delay between software I2C operations

// ============================================================================
// Debug Configuration
// ============================================================================

#define ENABLE_MULTI_SENSOR_DEBUG   1
#define ENABLE_TIMING_MEASUREMENT   1
#define ENABLE_MEMORY_MONITORING    1
#define ENABLE_TASK_MONITORING      1     // Enhanced task monitoring
#define ENABLE_I2C_BUS_MONITORING   1     // I2C bus specific monitoring

#if ENABLE_MULTI_SENSOR_DEBUG
#define MULTI_SENSOR_LOGD(tag, format, ...) ESP_LOGD(tag, format, ##__VA_ARGS__)
#define MULTI_SENSOR_LOGI(tag, format, ...) ESP_LOGI(tag, format, ##__VA_ARGS__)
#define MULTI_SENSOR_LOGW(tag, format, ...) ESP_LOGW(tag, format, ##__VA_ARGS__)
#define MULTI_SENSOR_LOGE(tag, format, ...) ESP_LOGE(tag, format, ##__VA_ARGS__)
#else
#define MULTI_SENSOR_LOGD(tag, format, ...)
#define MULTI_SENSOR_LOGI(tag, format, ...)
#define MULTI_SENSOR_LOGW(tag, format, ...)
#define MULTI_SENSOR_LOGE(tag, format, ...)
#endif

// ============================================================================
// Utility Macros - 4 Independent I2C Buses
// ============================================================================

#define SENSOR_NAME(id) (sensor_configs[id].name)
#define SENSOR_BUS_TYPE(id) (sensor_configs[id].bus_type)
#define SENSOR_ADDR(id) (sensor_configs[id].device_addr)

// Convert sensor ID to I2C bus type
#define SENSOR_TO_BUS_TYPE(id) ((i2c_bus_type_t)(id))

// All sensors use unified address
#define SENSOR_TO_ADDR(id) (MLX90640_ADDR_UNIFIED)

// Check if sensor uses hardware I2C (sensor_id maps directly to bus_type)
#define SENSOR_IS_HARDWARE_I2C(id) ((id) <= SENSOR_1)

// Check if sensor uses software I2C (sensor_id maps directly to bus_type)
#define SENSOR_IS_SOFTWARE_I2C(id) ((id) >= SENSOR_2)

// Get GPIO pins for I2C bus
#define GET_I2C_SDA_GPIO(bus_type) \
    ((bus_type) == I2C_BUS_HARDWARE_0 ? I2C_HW0_SDA_GPIO : \
     (bus_type) == I2C_BUS_HARDWARE_1 ? I2C_HW1_SDA_GPIO : \
     (bus_type) == I2C_BUS_SOFTWARE_2 ? I2C_SW2_SDA_GPIO : I2C_SW3_SDA_GPIO)

#define GET_I2C_SCL_GPIO(bus_type) \
    ((bus_type) == I2C_BUS_HARDWARE_0 ? I2C_HW0_SCL_GPIO : \
     (bus_type) == I2C_BUS_HARDWARE_1 ? I2C_HW1_SCL_GPIO : \
     (bus_type) == I2C_BUS_SOFTWARE_2 ? I2C_SW2_SCL_GPIO : I2C_SW3_SCL_GPIO)

// ============================================================================
// Global Configuration Table
// ============================================================================

extern const sensor_config_t sensor_configs[SENSOR_COUNT];

#ifdef __cplusplus
}
#endif

#endif // MLX90640_MULTI_CONFIG_H
