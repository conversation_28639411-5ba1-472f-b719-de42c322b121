#ifndef pre_PIXEL_THRESHOLDS_H
#define pre_PIXEL_THRESHOLDS_H

/**
 * @file pre_pixel_thresholds.h
 * @brief Personalized Pixel Thresholds for Thermal Anomaly Detection
 *
 * This file contains trained personalized threshold values for each pixel
 * of the MLX90640 thermal sensor (24x32 resolution).
 *
 * Generated from: pre_pixel_thresholds.npz
 * Generation time: 2025-07-07 15:51:05
 * Converter version: 1.0
 *
 * IMPORTANT: All threshold values have been multiplied by 10.0 for
 * optimization purposes in the C algorithm. The detection formula
 * uses (temp_change^2 / 10) which compensates for this scaling.
 *
 * Original data statistics:
 *   Min: 0.074238, Max: 0.681873
 *   Mean: 0.185428, Std: 0.093094
 *
 * Processed data statistics (×10):
 *   Min: 0.742385, Max: 6.818734
 *   Mean: 1.854285, Std: 0.930941
 *
 * Usage in thermal_anomaly_core.c:
 *   #include "pre_pixel_thresholds.h"
 *   thermal_detector_init(&detector, &config, pre_pixel_thresholds);
 *
 * <AUTHOR> Converter Script
 */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Personalized threshold array for MLX90640 thermal sensor
 *
 * Each element represents the variance threshold for the corresponding
 * pixel position. Values have been pre-multiplied by 10.0 for algorithm
 * optimization.
 *
 * Array dimensions: [row][column] = [24][32]
 * Data type: const float
 * Value range: See statistics in file header
 */
const float pre_pixel_thresholds[24][32] = {
    // Row  0
    {6.6533f, 5.4689f, 4.6119f, 4.4584f, 3.5220f, 2.9127f, 2.8288f, 2.3585f,
     2.0480f, 2.0032f, 1.7460f, 1.9666f, 1.9192f, 1.7706f, 1.9474f, 1.8490f,
     1.8524f, 1.8787f, 1.7357f, 1.5696f, 1.9677f, 1.6293f, 2.2008f, 1.9122f,
     2.5491f, 2.3763f, 2.8151f, 2.8115f, 3.8535f, 3.1952f, 4.8367f, 6.8187f},
    // Row  1
    {5.2969f, 4.4657f, 4.3493f, 3.9022f, 3.1235f, 3.1579f, 2.6784f, 2.4241f,
     2.3010f, 2.1058f, 2.1213f, 1.7845f, 1.6459f, 1.7328f, 1.5591f, 1.6918f,
     1.6140f, 1.5303f, 1.6691f, 1.5131f, 1.9298f, 1.6519f, 2.2270f, 1.8916f,
     2.1612f, 2.3292f, 2.7554f, 2.8192f, 3.1814f, 3.2630f, 4.4418f, 5.1782f},
    // Row  2
    {4.6160f, 3.5151f, 3.2139f, 3.2533f, 3.1020f, 2.4435f, 2.1474f, 2.2575f,
     1.9112f, 1.7523f, 1.7357f, 1.6149f, 1.3614f, 1.2229f, 1.4141f, 1.5148f,
     1.2109f, 1.3210f, 1.6545f, 1.4756f, 1.6952f, 1.5421f, 1.6463f, 1.8746f,
     2.3256f, 2.2971f, 2.3304f, 2.4187f, 2.9340f, 2.5051f, 3.2020f, 3.6958f},
    // Row  3
    {4.3129f, 4.0490f, 3.4422f, 3.3219f, 2.9482f, 2.3229f, 2.1461f, 2.1894f,
     1.7634f, 1.7729f, 1.4654f, 1.5754f, 1.3383f, 1.4094f, 1.2662f, 1.3553f,
     1.2289f, 1.1555f, 1.4923f, 1.2182f, 1.4561f, 1.3692f, 1.4252f, 1.5683f,
     1.7706f, 1.7932f, 2.1095f, 2.3697f, 2.9349f, 2.7034f, 3.3042f, 3.5945f},
    // Row  4
    {4.0851f, 3.4046f, 3.2404f, 3.3549f, 2.4181f, 2.3207f, 2.0782f, 2.1555f,
     1.7834f, 1.5633f, 1.4966f, 1.4857f, 1.2164f, 1.1091f, 1.1384f, 1.1010f,
     1.2902f, 1.1887f, 1.1529f, 1.0850f, 1.3695f, 1.3599f, 1.4787f, 1.3962f,
     1.7251f, 1.7685f, 1.9611f, 1.8636f, 2.0557f, 2.3509f, 2.8860f, 3.1999f},
    // Row  5
    {3.7856f, 3.2970f, 3.1603f, 3.0936f, 2.1667f, 1.8363f, 1.7783f, 1.7687f,
     1.6494f, 1.4509f, 1.4392f, 1.2543f, 1.2682f, 1.0610f, 1.0116f, 1.1491f,
     1.1073f, 0.9607f, 1.0999f, 1.0326f, 1.1189f, 1.1335f, 1.1641f, 1.4272f,
     1.4198f, 1.4828f, 2.0418f, 1.9290f, 2.1583f, 2.0980f, 2.8776f, 2.9523f},
    // Row  6
    {3.7076f, 3.2790f, 2.6997f, 2.2289f, 1.9334f, 1.9338f, 1.6718f, 1.6827f,
     1.4944f, 1.4349f, 1.3122f, 1.2137f, 1.1165f, 1.0279f, 0.8941f, 1.0078f,
     1.0998f, 0.9081f, 1.0392f, 1.0512f, 1.0526f, 1.1140f, 1.2338f, 1.2651f,
     1.4503f, 1.4199f, 1.6755f, 1.6503f, 1.8966f, 1.9447f, 2.8340f, 2.4419f},
    // Row  7
    {3.1947f, 3.3174f, 2.7275f, 2.4345f, 1.9757f, 1.8463f, 1.5542f, 1.5897f,
     1.4561f, 1.2280f, 1.1518f, 1.0553f, 1.0564f, 0.9449f, 0.9285f, 0.8213f,
     1.0109f, 0.9333f, 1.0023f, 1.0548f, 1.0382f, 1.0539f, 1.1039f, 1.2570f,
     1.3385f, 1.2888f, 1.6153f, 1.4307f, 2.0482f, 1.9856f, 2.8228f, 2.8021f},
    // Row  8
    {5.7107f, 2.8368f, 2.6418f, 2.3131f, 2.0664f, 1.7557f, 1.5475f, 1.4394f,
     1.3766f, 1.0857f, 1.2589f, 1.0323f, 0.9438f, 0.9273f, 0.9816f, 0.8830f,
     0.9825f, 0.8864f, 1.0771f, 0.9408f, 1.1034f, 0.9523f, 1.1978f, 1.0932f,
     1.3440f, 1.1512f, 1.5745f, 1.8048f, 2.0454f, 1.9686f, 2.7690f, 2.3903f},
    // Row  9
    {3.8741f, 3.2967f, 2.3784f, 2.2216f, 1.8730f, 1.5733f, 1.3860f, 1.4285f,
     1.3900f, 1.3037f, 1.1440f, 1.1653f, 0.9454f, 0.8701f, 0.8941f, 0.9654f,
     0.8784f, 1.0692f, 0.9260f, 0.9449f, 0.9694f, 1.0026f, 0.9985f, 1.3031f,
     1.3274f, 1.2158f, 1.5310f, 1.5133f, 1.8575f, 1.9844f, 2.3682f, 2.3848f},
    // Row 10
    {2.7727f, 2.5563f, 2.2042f, 2.2388f, 1.7040f, 1.6602f, 1.4245f, 1.3826f,
     1.1920f, 1.1232f, 1.0167f, 0.8823f, 0.8972f, 0.8271f, 0.9275f, 0.9205f,
     0.9259f, 0.8492f, 1.0991f, 0.9540f, 0.8319f, 1.0839f, 1.0172f, 1.1376f,
     1.1797f, 1.2789f, 1.2886f, 1.4634f, 1.8060f, 1.6245f, 2.2441f, 2.2735f},
    // Row 11
    {2.9538f, 2.4147f, 2.0524f, 2.4443f, 1.8974f, 1.6137f, 1.3990f, 1.3620f,
     1.2878f, 1.0582f, 0.9338f, 0.9313f, 0.7697f, 0.8397f, 0.8304f, 0.8565f,
     0.9613f, 0.8788f, 0.9038f, 0.9295f, 0.8541f, 0.8341f, 0.9609f, 1.2019f,
     1.2535f, 1.0412f, 1.3159f, 1.5912f, 1.5974f, 1.5863f, 2.0230f, 2.1437f},
    // Row 12
    {3.0790f, 2.6537f, 2.2332f, 2.2086f, 1.5424f, 1.5995f, 1.4843f, 1.2557f,
     1.1817f, 0.9900f, 0.9654f, 1.0265f, 0.8799f, 0.9987f, 0.8700f, 0.8642f,
     0.8788f, 0.9704f, 0.8322f, 0.9837f, 0.9737f, 0.8498f, 1.0466f, 1.0328f,
     1.3813f, 1.1911f, 1.4479f, 1.6636f, 1.8205f, 1.6552f, 2.2860f, 2.4615f},
    // Row 13
    {2.9026f, 2.6855f, 2.1739f, 1.8252f, 1.6659f, 1.6822f, 1.4311f, 1.4026f,
     1.1093f, 0.9709f, 0.8687f, 0.8888f, 1.0023f, 0.9316f, 0.9081f, 0.7424f,
     0.9323f, 0.8731f, 0.9481f, 1.0050f, 0.9728f, 0.8473f, 1.1282f, 1.0702f,
     1.2014f, 1.1989f, 1.4220f, 1.6228f, 1.7030f, 1.6755f, 2.2565f, 2.5328f},
    // Row 14
    {2.7929f, 3.0258f, 2.0214f, 2.1925f, 1.9629f, 1.8590f, 1.4914f, 1.2100f,
     1.5319f, 1.1436f, 1.0363f, 0.8943f, 0.9822f, 0.9332f, 0.9593f, 0.8520f,
     0.8281f, 0.8706f, 0.8527f, 0.8605f, 1.0969f, 1.0388f, 1.0419f, 1.1706f,
     1.2224f, 1.2720f, 1.7612f, 1.6194f, 2.3371f, 2.2039f, 2.9581f, 2.9298f},
    // Row 15
    {2.6383f, 2.7231f, 2.2140f, 2.0903f, 1.7087f, 1.4848f, 1.3839f, 1.3477f,
     1.4382f, 1.0604f, 0.9940f, 0.9772f, 0.9688f, 0.8928f, 0.8710f, 0.8974f,
     0.8506f, 0.8486f, 0.9197f, 0.9685f, 0.9320f, 0.8638f, 1.0281f, 1.1456f,
     1.2881f, 1.4414f, 1.3782f, 1.7710f, 2.5623f, 2.3927f, 2.5777f, 2.7756f},
    // Row 16
    {3.2456f, 2.7803f, 2.5059f, 2.0045f, 2.2601f, 1.9279f, 1.6671f, 1.4648f,
     1.2780f, 1.2425f, 1.2012f, 1.0842f, 0.9514f, 0.8029f, 0.8833f, 0.9075f,
     1.0175f, 1.0241f, 1.0960f, 1.1479f, 1.2652f, 1.1069f, 1.1788f, 1.0714f,
     1.1229f, 1.3410f, 1.5969f, 1.6239f, 2.2670f, 2.1275f, 2.6801f, 2.9258f},
    // Row 17
    {3.5102f, 2.8846f, 2.2951f, 2.4052f, 2.1233f, 1.7440f, 1.6196f, 1.5485f,
     1.3721f, 1.1986f, 1.1709f, 1.0930f, 1.0253f, 0.9373f, 0.9394f, 1.0260f,
     0.8641f, 0.7925f, 1.0642f, 1.0902f, 1.2277f, 1.0844f, 1.1368f, 1.1511f,
     1.3042f, 1.3780f, 1.5430f, 1.8518f, 1.8674f, 1.9301f, 2.1503f, 2.8409f},
    // Row 18
    {3.6851f, 3.1325f, 2.9623f, 2.5741f, 2.2125f, 1.8798f, 1.9078f, 1.8459f,
     1.6160f, 1.4500f, 1.2570f, 1.2128f, 1.1566f, 1.0190f, 1.1335f, 1.0226f,
     1.0993f, 0.9216f, 1.1346f, 1.0673f, 1.0263f, 1.0028f, 1.1666f, 1.5508f,
     1.5366f, 1.4979f, 1.7481f, 1.8996f, 2.4786f, 2.2862f, 2.7161f, 2.5883f},
    // Row 19
    {3.9476f, 3.5128f, 2.7542f, 2.6232f, 2.3943f, 2.1215f, 1.9257f, 1.6847f,
     1.5002f, 1.5342f, 1.1951f, 1.2664f, 1.1242f, 1.0775f, 1.1299f, 0.8821f,
     1.0878f, 0.9663f, 1.0771f, 1.1156f, 1.0643f, 1.2254f, 1.2107f, 1.5128f,
     1.6923f, 1.6645f, 1.7858f, 1.8015f, 2.4073f, 2.1956f, 2.9435f, 2.9409f},
    // Row 20
    {4.6939f, 4.2619f, 2.9947f, 2.7222f, 2.5424f, 2.3667f, 2.2792f, 2.0815f,
     1.9926f, 1.6521f, 1.5437f, 1.3456f, 1.3643f, 1.2933f, 1.1391f, 1.2019f,
     1.1521f, 1.1753f, 1.2331f, 1.1296f, 1.3209f, 1.2364f, 1.2606f, 1.5137f,
     1.8830f, 1.6962f, 2.2338f, 2.0060f, 2.5734f, 2.5573f, 3.1194f, 3.2792f},
    // Row 21
    {4.8024f, 4.1707f, 3.1369f, 3.3634f, 2.3579f, 2.5180f, 2.3056f, 2.0876f,
     1.6933f, 1.7776f, 1.3731f, 1.5201f, 1.4729f, 1.2807f, 1.2822f, 1.3370f,
     1.4446f, 1.2832f, 1.1787f, 1.2197f, 1.4006f, 1.2601f, 1.6111f, 1.6149f,
     1.8655f, 1.7814f, 2.0372f, 2.0987f, 2.4852f, 2.6236f, 3.3559f, 3.6621f},
    // Row 22
    {4.9640f, 3.8635f, 3.6287f, 3.4329f, 3.2606f, 3.1527f, 2.6538f, 2.4293f,
     2.4944f, 2.1379f, 2.1531f, 2.0376f, 1.7757f, 1.5971f, 1.5745f, 1.4946f,
     1.3011f, 1.3940f, 1.3874f, 1.6174f, 1.6273f, 1.5117f, 2.3677f, 1.7505f,
     2.0497f, 2.2502f, 2.7327f, 2.4007f, 3.3982f, 3.0190f, 4.0330f, 4.3439f},
    // Row 23
    {5.3412f, 4.7657f, 3.9163f, 3.7760f, 3.6079f, 2.9965f, 2.8489f, 2.6026f,
     2.4300f, 2.2347f, 1.8653f, 1.7313f, 1.5009f, 1.5820f, 1.4491f, 1.5854f,
     1.5433f, 1.3493f, 1.4885f, 1.4796f, 1.7961f, 1.5325f, 2.1127f, 1.7802f,
     2.0560f, 2.2864f, 2.6698f, 2.3660f, 3.5304f, 3.0103f, 4.7132f, 5.1048f}
};

#ifdef __cplusplus
}
#endif

#endif // pre_PIXEL_THRESHOLDS_H
