/**
 * @file multi_sensor_tasks.h
 * @brief Multi-sensor dual-core task system
 * 
 * This file defines the dual-core task architecture for the 4-sensor
 * MLX90640 system with Core 0 handling data acquisition and Core 1
 * handling data processing and thermal detection.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MULTI_SENSOR_TASKS_H
#define MULTI_SENSOR_TASKS_H

#include <stdint.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_err.h"
#include "mlx90640_multi_config.h"
#include "mlx90640_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// Task Configuration
// ============================================================================

/**
 * @brief Task core assignments
 */
#define ACQUIRE_TASK_CORE       0    ///< Core 0 for data acquisition
#define PROCESS_TASK_CORE       1    ///< Core 1 for data processing

/**
 * @brief Task naming conventions
 */
#define ACQUIRE_TASK_NAME_FMT   "acquire_%s"
#define PROCESS_TASK_NAME_FMT   "process_%s"

/**
 * @brief Task notification values
 */
#define TASK_NOTIFY_DATA_READY  0x01
#define TASK_NOTIFY_STOP        0x02
#define TASK_NOTIFY_ERROR       0x04

// ============================================================================
// Task State Management
// ============================================================================

/**
 * @brief Task state enumeration
 */
typedef enum {
    TASK_STATE_STOPPED = 0,     ///< Task is stopped
    TASK_STATE_STARTING,        ///< Task is starting up
    TASK_STATE_RUNNING,         ///< Task is running normally
    TASK_STATE_STOPPING,       ///< Task is stopping
    TASK_STATE_ERROR            ///< Task is in error state
} task_state_t;

// task_stats_t is now defined in mlx90640_instance.h to avoid circular dependency

/**
 * @brief Task control structure
 */
typedef struct {
    TaskHandle_t task_handle;       ///< FreeRTOS task handle
    task_state_t state;             ///< Current task state
    task_stats_t stats;             ///< Task statistics
    uint32_t start_time;            ///< Task start timestamp
    char task_name[32];             ///< Task name string
} task_control_t;

// ============================================================================
// Task Creation and Management Functions
// ============================================================================

/**
 * @brief Create all sensor tasks
 * 
 * Creates acquisition and processing tasks for all 4 sensors.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_create_all_tasks(void);

/**
 * @brief Delete all sensor tasks
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_delete_all_tasks(void);

/**
 * @brief Create tasks for a specific sensor
 * 
 * @param instance Pointer to sensor instance
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_create_sensor_tasks(mlx90640_instance_t* instance);

/**
 * @brief Delete tasks for a specific sensor
 * 
 * @param instance Pointer to sensor instance
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_delete_sensor_tasks(mlx90640_instance_t* instance);

/**
 * @brief Start all sensor tasks
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_start_all_tasks(void);

/**
 * @brief Stop all sensor tasks
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_stop_all_tasks(void);

// ============================================================================
// Core Task Functions
// ============================================================================

/**
 * @brief Data acquisition task function (Core 0)
 * 
 * This task runs on Core 0 and is responsible for:
 * - Reading data from MLX90640 sensor
 * - Converting raw data to temperature values
 * - Notifying processing task when data is ready
 * - Maintaining acquisition timing (~10 FPS)
 * 
 * @param param Pointer to mlx90640_instance_t
 */
void multi_sensor_acquire_task(void* param);

/**
 * @brief Data processing task function (Core 1)
 * 
 * This task runs on Core 1 and is responsible for:
 * - Waiting for data ready notifications
 * - Performing thermal anomaly detection
 * - Processing detection results
 * - Handling UART output and logging
 * 
 * @param param Pointer to mlx90640_instance_t
 */
void multi_sensor_process_task(void* param);

// ============================================================================
// Task Communication Functions
// ============================================================================

/**
 * @brief Send data ready notification
 * 
 * @param instance Pointer to sensor instance
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_notify_data_ready(mlx90640_instance_t* instance);

/**
 * @brief Wait for data ready notification
 * 
 * @param instance Pointer to sensor instance
 * @param timeout_ms Timeout in milliseconds
 * @return ESP_OK on success, error code on timeout/failure
 */
esp_err_t multi_sensor_wait_data_ready(mlx90640_instance_t* instance, uint32_t timeout_ms);

/**
 * @brief Send stop notification to tasks
 * 
 * @param instance Pointer to sensor instance
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_notify_stop(mlx90640_instance_t* instance);

// ============================================================================
// Task Monitoring Functions
// ============================================================================

/**
 * @brief Get task statistics
 * 
 * @param instance Pointer to sensor instance
 * @param acquire_stats Output: acquisition task stats (can be NULL)
 * @param process_stats Output: processing task stats (can be NULL)
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_get_task_stats(mlx90640_instance_t* instance,
                                     task_stats_t* acquire_stats,
                                     task_stats_t* process_stats);

/**
 * @brief Reset task statistics
 * 
 * @param instance Pointer to sensor instance
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_reset_task_stats(mlx90640_instance_t* instance);

/**
 * @brief Check if all tasks are healthy
 * 
 * @return true if all tasks are running normally, false otherwise
 */
bool multi_sensor_all_tasks_healthy(void);

/**
 * @brief Log task status for all sensors
 */
void multi_sensor_log_task_status(void);

// ============================================================================
// Task Utility Functions
// ============================================================================

/**
 * @brief Get task state name string
 * 
 * @param state Task state
 * @return String representation of task state
 */
const char* multi_sensor_get_task_state_name(task_state_t state);

/**
 * @brief Update task statistics
 * 
 * @param stats Pointer to task statistics structure
 * @param execution_time_us Execution time in microseconds
 * @param success Whether the iteration was successful
 */
void multi_sensor_update_task_stats(task_stats_t* stats, uint32_t execution_time_us, bool success);

/**
 * @brief Initialize task control structure
 * 
 * @param control Pointer to task control structure
 * @param task_name Task name
 */
void multi_sensor_init_task_control(task_control_t* control, const char* task_name);

// ============================================================================
// Error Handling Functions
// ============================================================================

/**
 * @brief Handle task error
 * 
 * @param instance Pointer to sensor instance
 * @param task_name Name of the task that encountered error
 * @param error_code Error code
 * @param error_msg Error message
 * @return ESP_OK if error handled, error code otherwise
 */
esp_err_t multi_sensor_handle_task_error(mlx90640_instance_t* instance,
                                        const char* task_name,
                                        esp_err_t error_code,
                                        const char* error_msg);

/**
 * @brief Restart failed task
 * 
 * @param instance Pointer to sensor instance
 * @param is_acquire_task true for acquire task, false for process task
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_restart_task(mlx90640_instance_t* instance, bool is_acquire_task);

// ============================================================================
// Performance Monitoring Functions
// ============================================================================

/**
 * @brief Get system-wide task performance metrics
 * 
 * @param total_fps Output: total frames per second across all sensors
 * @param avg_process_time_us Output: average processing time
 * @param max_process_time_us Output: maximum processing time
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_get_performance_metrics(float* total_fps,
                                              uint32_t* avg_process_time_us,
                                              uint32_t* max_process_time_us);

/**
 * @brief Log performance summary
 */
void multi_sensor_log_performance_summary(void);

/**
 * @brief Get I2C bus specific performance metrics
 *
 * @param hw_i2c_fps Output: Hardware I2C total FPS
 * @param sw_i2c_fps Output: Software I2C total FPS
 * @param hw_i2c_errors Output: Hardware I2C error count
 * @param sw_i2c_errors Output: Software I2C error count
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_get_i2c_bus_metrics(float* hw_i2c_fps, float* sw_i2c_fps,
                                          uint32_t* hw_i2c_errors, uint32_t* sw_i2c_errors);

/**
 * @brief Log I2C bus specific performance
 */
void multi_sensor_log_i2c_bus_performance(void);

/**
 * @brief Check if software I2C tasks need priority boost
 *
 * @return true if priority boost is needed, false otherwise
 */
bool multi_sensor_sw_i2c_needs_priority_boost(void);

/**
 * @brief Apply dynamic task priority adjustment
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t multi_sensor_adjust_task_priorities(void);

#ifdef __cplusplus
}
#endif

#endif // MULTI_SENSOR_TASKS_H
