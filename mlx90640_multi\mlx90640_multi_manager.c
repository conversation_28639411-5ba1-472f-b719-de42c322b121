/**
 * @file mlx90640_multi_manager.c
 * @brief Multi-sensor system manager implementation
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include "mlx90640_multi_manager.h"
#include "mlx90640_i2c_multi.h"
#include "multi_sensor_init.h"
#include "multi_sensor_tasks.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "MLX90640_MULTI_MGR";

// ============================================================================
// Dynamic Memory Allocation - Reduce DRAM usage
// ============================================================================

// Use heap allocation for large buffers to avoid DRAM overflow
static float* sensor_data_Ax = NULL;
static float* sensor_data_Ay = NULL;
static float* sensor_data_Bx = NULL;
static float* sensor_data_By = NULL;

// Thermal detector instances (smaller, can be static)
static thermal_detector_t detector_Ax;
static thermal_detector_t detector_Ay;
static thermal_detector_t detector_Bx;
static thermal_detector_t detector_By;

// Global multi-sensor manager instance
static mlx90640_multi_manager_t g_multi_manager;

// Memory allocation lookup tables (will be set after allocation)
static float* sensor_data_ptrs[SENSOR_COUNT] = {NULL, NULL, NULL, NULL};

static thermal_detector_t* detector_ptrs[SENSOR_COUNT] = {
    &detector_Ax,    // SENSOR_AX
    &detector_Ay,    // SENSOR_AY
    &detector_Bx,    // SENSOR_BX
    &detector_By     // SENSOR_BY
};

// ============================================================================
// Manager Access Functions
// ============================================================================

mlx90640_multi_manager_t* mlx90640_multi_get_manager(void)
{
    return &g_multi_manager;
}

// ============================================================================
// System Management Functions
// ============================================================================

esp_err_t mlx90640_multi_system_init(const thermal_config_t* config)
{
    esp_err_t ret;
    mlx90640_multi_manager_t* manager = &g_multi_manager;
    
    MULTI_SENSOR_LOGI(TAG, "Initializing multi-sensor system with 4 MLX90640 sensors");
    
    // Check if already initialized
    if (manager->initialized) {
        MULTI_SENSOR_LOGW(TAG, "System already initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    // Allocate memory for temperature data buffers using SPIRAM
    MULTI_SENSOR_LOGI(TAG, "Allocating memory for sensor data buffers in SPIRAM...");
    sensor_data_Ax = (float*)heap_caps_malloc(TEMP_DATA_SIZE, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    sensor_data_Ay = (float*)heap_caps_malloc(TEMP_DATA_SIZE, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    sensor_data_Bx = (float*)heap_caps_malloc(TEMP_DATA_SIZE, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    sensor_data_By = (float*)heap_caps_malloc(TEMP_DATA_SIZE, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);

    if (!sensor_data_Ax || !sensor_data_Ay || !sensor_data_Bx || !sensor_data_By) {
        MULTI_SENSOR_LOGE(TAG, "Failed to allocate memory for sensor data buffers");
        // Cleanup any allocated memory
        if (sensor_data_Ax) free(sensor_data_Ax);
        if (sensor_data_Ay) free(sensor_data_Ay);
        if (sensor_data_Bx) free(sensor_data_Bx);
        if (sensor_data_By) free(sensor_data_By);
        return ESP_ERR_NO_MEM;
    }

    // Update lookup table
    sensor_data_ptrs[SENSOR_0] = sensor_data_Ax;
    sensor_data_ptrs[SENSOR_1] = sensor_data_Ay;
    sensor_data_ptrs[SENSOR_2] = sensor_data_Bx;
    sensor_data_ptrs[SENSOR_3] = sensor_data_By;

    MULTI_SENSOR_LOGI(TAG, "Memory allocated: %u bytes total",
                     (unsigned)(4 * TEMP_DATA_SIZE));

    // Clear manager structure
    memset(manager, 0, sizeof(mlx90640_multi_manager_t));
    manager->state = MULTI_SYSTEM_INITIALIZING;
    manager->start_time = esp_timer_get_time() / 1000000; // Convert to seconds
    
    // Set default configuration if none provided
    if (config != NULL) {
        memcpy(&manager->config, config, sizeof(thermal_config_t));
    } else {
        // Use default thermal detection configuration
        thermal_config_t default_config = {
            .pre_filter_min = 0.0f,
            .pre_filter_max = 80.0f,
            .pre_filter_delta = 10.0f,
            .window_size = 20,
            .center_distance_threshold = 5.0f,
            .center_distance_threshold_squared = 25.0f,
            .min_anomaly_pixels = 5,
            .enable_pre_filter = true,
            .enable_learning = true,
            .warmup_time_ms = 5000
        };
        memcpy(&manager->config, &default_config, sizeof(thermal_config_t));
    }
    
    MULTI_SENSOR_LOGI(TAG, "Using thermal config: learning=%d, window_size=%d, warmup_ms=%u",
                     manager->config.enable_learning,
                     manager->config.window_size,
                     (unsigned)manager->config.warmup_time_ms);
    
    // Step 1: Initialize hardware (GPIO, I2C, sensors)
    ret = multi_sensor_hardware_init();
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Hardware initialization failed: %s", esp_err_to_name(ret));
        mlx90640_multi_set_error(ret, "Hardware initialization failed");
        manager->state = MULTI_SYSTEM_ERROR;
        return ret;
    }
    
    // Step 2: Initialize all sensor instances
    for (int i = 0; i < SENSOR_COUNT; i++) {
        ret = mlx90640_instance_init(&manager->sensors[i],
                                   (sensor_id_t)i,
                                   sensor_data_ptrs[i],
                                   detector_ptrs[i],
                                   &manager->config);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to initialize sensor %s: %s", 
                             mlx90640_get_sensor_name((sensor_id_t)i), esp_err_to_name(ret));
            mlx90640_multi_set_error(ret, "Sensor instance initialization failed");
            manager->state = MULTI_SYSTEM_ERROR;
            return ret;
        }
        MULTI_SENSOR_LOGI(TAG, "Sensor %s initialized successfully", 
                         mlx90640_get_sensor_name((sensor_id_t)i));
    }
    
    // Step 3: Initialize system statistics
    memset(&manager->stats, 0, sizeof(multi_system_stats_t));
    manager->stats.memory_usage_bytes = mlx90640_multi_get_memory_usage();
    
    // Mark as initialized
    manager->initialized = true;
    manager->state = MULTI_SYSTEM_READY;
    
    MULTI_SENSOR_LOGI(TAG, "Multi-sensor system initialized successfully");
    MULTI_SENSOR_LOGI(TAG, "Memory usage: %u bytes", (unsigned)manager->stats.memory_usage_bytes);
    
    return ESP_OK;
}

esp_err_t mlx90640_multi_system_start(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;
    esp_err_t ret;
    
    if (!manager->initialized || manager->state != MULTI_SYSTEM_READY) {
        MULTI_SENSOR_LOGE(TAG, "System not ready for start (state: %s)", 
                         mlx90640_multi_get_state_name(manager->state));
        return ESP_ERR_INVALID_STATE;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Starting multi-sensor system tasks");
    manager->state = MULTI_SYSTEM_RUNNING;
    
    // Start all sensor tasks
    for (int i = 0; i < SENSOR_COUNT; i++) {
        ret = mlx90640_instance_start_tasks(&manager->sensors[i]);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to start tasks for sensor %s: %s",
                             mlx90640_get_sensor_name((sensor_id_t)i), esp_err_to_name(ret));
            // Try to stop already started tasks
            mlx90640_multi_system_stop();
            return ret;
        }
        MULTI_SENSOR_LOGI(TAG, "Tasks started for sensor %s", 
                         mlx90640_get_sensor_name((sensor_id_t)i));
    }
    
    MULTI_SENSOR_LOGI(TAG, "All sensor tasks started successfully");
    return ESP_OK;
}

esp_err_t mlx90640_multi_system_stop(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;
    
    if (manager->state != MULTI_SYSTEM_RUNNING) {
        MULTI_SENSOR_LOGW(TAG, "System not running (state: %s)", 
                         mlx90640_multi_get_state_name(manager->state));
        return ESP_ERR_INVALID_STATE;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Stopping multi-sensor system");
    manager->state = MULTI_SYSTEM_STOPPING;
    
    // Stop all sensor tasks
    for (int i = 0; i < SENSOR_COUNT; i++) {
        esp_err_t ret = mlx90640_instance_stop_tasks(&manager->sensors[i]);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGW(TAG, "Failed to stop tasks for sensor %s: %s",
                             mlx90640_get_sensor_name((sensor_id_t)i), esp_err_to_name(ret));
        }
    }
    
    manager->state = MULTI_SYSTEM_READY;
    MULTI_SENSOR_LOGI(TAG, "Multi-sensor system stopped");
    return ESP_OK;
}

esp_err_t mlx90640_multi_system_shutdown(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;
    
    MULTI_SENSOR_LOGI(TAG, "Shutting down multi-sensor system");
    
    // Stop system if running
    if (manager->state == MULTI_SYSTEM_RUNNING) {
        mlx90640_multi_system_stop();
    }
    
    // Deinitialize all sensor instances
    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_deinit(&manager->sensors[i]);
    }
    
    // Deinitialize hardware
    mlx90640_i2c_multi_deinit();

    // Free allocated memory
    MULTI_SENSOR_LOGI(TAG, "Freeing sensor data buffers...");
    if (sensor_data_Ax) { free(sensor_data_Ax); sensor_data_Ax = NULL; }
    if (sensor_data_Ay) { free(sensor_data_Ay); sensor_data_Ay = NULL; }
    if (sensor_data_Bx) { free(sensor_data_Bx); sensor_data_Bx = NULL; }
    if (sensor_data_By) { free(sensor_data_By); sensor_data_By = NULL; }

    // Clear lookup table
    for (int i = 0; i < SENSOR_COUNT; i++) {
        sensor_data_ptrs[i] = NULL;
    }

    // Clear manager state
    manager->initialized = false;
    manager->state = MULTI_SYSTEM_UNINITIALIZED;
    
    MULTI_SENSOR_LOGI(TAG, "Multi-sensor system shutdown complete");
    return ESP_OK;
}

multi_system_state_t mlx90640_multi_get_system_state(void)
{
    return g_multi_manager.state;
}

bool mlx90640_multi_system_is_healthy(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;
    
    if (!manager->initialized || manager->state != MULTI_SYSTEM_RUNNING) {
        return false;
    }
    
    // Check if all sensors are healthy
    for (int i = 0; i < SENSOR_COUNT; i++) {
        if (!mlx90640_instance_is_healthy(&manager->sensors[i])) {
            return false;
        }
    }
    
    return true;
}

// ============================================================================
// Sensor Access Functions
// ============================================================================

mlx90640_instance_t* mlx90640_multi_get_sensor(sensor_id_t sensor_id)
{
    if (sensor_id >= SENSOR_COUNT) {
        return NULL;
    }
    return &g_multi_manager.sensors[sensor_id];
}

uint8_t mlx90640_multi_get_sensor_count(void)
{
    return SENSOR_COUNT;
}

bool mlx90640_multi_is_sensor_active(sensor_id_t sensor_id)
{
    mlx90640_instance_t* instance = mlx90640_multi_get_sensor(sensor_id);
    if (instance == NULL) {
        return false;
    }
    return instance->is_active;
}

// ============================================================================
// Statistics and Monitoring Functions
// ============================================================================

esp_err_t mlx90640_multi_get_stats(multi_system_stats_t* stats)
{
    if (stats == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    mlx90640_multi_manager_t* manager = &g_multi_manager;

    // Update statistics before returning
    mlx90640_multi_update_stats();

    memcpy(stats, &manager->stats, sizeof(multi_system_stats_t));
    return ESP_OK;
}

esp_err_t mlx90640_multi_reset_stats(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;

    // Reset system-wide statistics
    manager->stats.total_frames_processed = 0;
    manager->stats.total_anomalies_detected = 0;
    manager->stats.total_errors = 0;
    manager->stats.avg_fps = 0.0f;

    // Reset individual sensor statistics
    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_reset_stats(&manager->sensors[i]);
    }

    MULTI_SENSOR_LOGI(TAG, "System statistics reset");
    return ESP_OK;
}

esp_err_t mlx90640_multi_update_stats(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;

    // Calculate uptime
    uint32_t current_time = esp_timer_get_time() / 1000000;
    manager->stats.uptime_seconds = current_time - manager->start_time;

    // Aggregate statistics from all sensors
    uint32_t total_frames = 0;
    uint32_t total_errors = 0;

    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_t* instance = &manager->sensors[i];
        total_frames += instance->frame_count;
        total_errors += instance->error_count;
    }

    manager->stats.total_frames_processed = total_frames;
    manager->stats.total_errors = total_errors;

    // Calculate average FPS
    if (manager->stats.uptime_seconds > 0) {
        manager->stats.avg_fps = (float)total_frames / manager->stats.uptime_seconds;
    }

    return ESP_OK;
}

void mlx90640_multi_log_system_status(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;

    MULTI_SENSOR_LOGI(TAG, "=== Multi-Sensor System Status ===");
    MULTI_SENSOR_LOGI(TAG, "State: %s", mlx90640_multi_get_state_name(manager->state));
    MULTI_SENSOR_LOGI(TAG, "Initialized: %s", manager->initialized ? "Yes" : "No");
    MULTI_SENSOR_LOGI(TAG, "Uptime: %u seconds", (unsigned)manager->stats.uptime_seconds);
    MULTI_SENSOR_LOGI(TAG, "Total frames: %u", (unsigned)manager->stats.total_frames_processed);
    MULTI_SENSOR_LOGI(TAG, "Total errors: %u", (unsigned)manager->stats.total_errors);
    MULTI_SENSOR_LOGI(TAG, "Average FPS: %.2f", manager->stats.avg_fps);
    MULTI_SENSOR_LOGI(TAG, "Memory usage: %u bytes", (unsigned)manager->stats.memory_usage_bytes);

    // Log individual sensor status
    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_log_status(&manager->sensors[i]);
    }
}

// ============================================================================
// Configuration Functions
// ============================================================================

esp_err_t mlx90640_multi_get_config(thermal_config_t* config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    memcpy(config, &g_multi_manager.config, sizeof(thermal_config_t));
    return ESP_OK;
}

esp_err_t mlx90640_multi_update_config(const thermal_config_t* config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    mlx90640_multi_manager_t* manager = &g_multi_manager;

    // System must be stopped to update configuration
    if (manager->state == MULTI_SYSTEM_RUNNING) {
        MULTI_SENSOR_LOGE(TAG, "Cannot update config while system is running");
        return ESP_ERR_INVALID_STATE;
    }

    memcpy(&manager->config, config, sizeof(thermal_config_t));

    // Update configuration for all sensor instances
    for (int i = 0; i < SENSOR_COUNT; i++) {
        // TODO: Add instance config update function if needed
        // mlx90640_instance_update_config(&manager->sensors[i], config);
    }

    MULTI_SENSOR_LOGI(TAG, "System configuration updated");
    return ESP_OK;
}

// ============================================================================
// Error Handling Functions
// ============================================================================

esp_err_t mlx90640_multi_get_last_error(esp_err_t* error_code, char* error_msg, size_t msg_size)
{
    if (error_code == NULL || error_msg == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    mlx90640_multi_manager_t* manager = &g_multi_manager;

    if (manager->last_error_code == ESP_OK) {
        return ESP_ERR_NOT_FOUND;
    }

    *error_code = manager->last_error_code;
    strncpy(error_msg, manager->last_error_msg, msg_size - 1);
    error_msg[msg_size - 1] = '\0';

    return ESP_OK;
}

esp_err_t mlx90640_multi_clear_last_error(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;

    manager->last_error_code = ESP_OK;
    manager->last_error_time = 0;
    memset(manager->last_error_msg, 0, sizeof(manager->last_error_msg));

    return ESP_OK;
}

esp_err_t mlx90640_multi_set_error(esp_err_t error_code, const char* error_msg)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;

    manager->last_error_code = error_code;
    manager->last_error_time = esp_timer_get_time() / 1000000;

    if (error_msg != NULL) {
        strncpy(manager->last_error_msg, error_msg, sizeof(manager->last_error_msg) - 1);
        manager->last_error_msg[sizeof(manager->last_error_msg) - 1] = '\0';
    } else {
        snprintf(manager->last_error_msg, sizeof(manager->last_error_msg),
                "Error: %s", esp_err_to_name(error_code));
    }

    MULTI_SENSOR_LOGE(TAG, "System error set: %s - %s",
                     esp_err_to_name(error_code), manager->last_error_msg);

    return ESP_OK;
}

// ============================================================================
// Utility Functions
// ============================================================================

const char* mlx90640_multi_get_state_name(multi_system_state_t state)
{
    switch (state) {
        case MULTI_SYSTEM_UNINITIALIZED: return "UNINITIALIZED";
        case MULTI_SYSTEM_INITIALIZING:  return "INITIALIZING";
        case MULTI_SYSTEM_READY:         return "READY";
        case MULTI_SYSTEM_RUNNING:       return "RUNNING";
        case MULTI_SYSTEM_ERROR:         return "ERROR";
        case MULTI_SYSTEM_STOPPING:     return "STOPPING";
        default:                         return "UNKNOWN";
    }
}

uint32_t mlx90640_multi_get_uptime_seconds(void)
{
    mlx90640_multi_manager_t* manager = &g_multi_manager;
    if (!manager->initialized) {
        return 0;
    }

    uint32_t current_time = esp_timer_get_time() / 1000000;
    return current_time - manager->start_time;
}

uint32_t mlx90640_multi_get_memory_usage(void)
{
    uint32_t total_memory = 0;

    // Static memory allocation
    total_memory += SENSOR_COUNT * THERMAL_PIXELS * sizeof(float);  // Temperature data buffers
    total_memory += SENSOR_COUNT * sizeof(thermal_detector_t);      // Thermal detector instances
    total_memory += sizeof(mlx90640_multi_manager_t);               // Manager instance

    // Task stacks (estimated) - different sizes for HW vs SW I2C
    total_memory += 2 * (ACQUIRE_TASK_STACK_HW + PROCESS_TASK_STACK);  // 2 HW I2C sensors
    total_memory += 2 * (ACQUIRE_TASK_STACK_SW + PROCESS_TASK_STACK);  // 2 SW I2C sensors

    // Queues and semaphores (estimated)
    total_memory += SENSOR_COUNT * (DATA_QUEUE_LENGTH * DATA_QUEUE_ITEM_SIZE + 100);

    return total_memory;
}
