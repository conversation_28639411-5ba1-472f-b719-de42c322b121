# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-07-01 15:12:55 - Log of updates made.

*

## Coding Patterns

* **Circular Buffer with Running Sum**: Efficient sliding window implementation using running sum matrix to avoid O(n²) recalculation
  - Add `pixel_history_sum` matrix to track cumulative values
  - On buffer update: subtract old frame, add new frame, calculate mean from sum
  - Reduces complexity from O(window_size × pixels) to O(pixels)

* **1D to 2D Array Interface Pattern**: Provide both native 1D and 2D interfaces for sensor data processing
  - Keep existing 2D algorithms for readability
  - Add 1D wrapper functions for direct sensor integration
  - Use static conversion buffers to minimize memory allocation

* **Inline Chess Mask Calculation**: Replace pre-computed arrays with inline calculations
  - `check_chess_mask(i, j, even_frame)` using modulo arithmetic
  - Eliminates 768-element boolean array allocation
  - Improves memory efficiency on embedded systems

* **State Machine with Learning Pattern**: Adaptive threshold generation through online learning
  - Three-stage state machine: WARMUP → LEARNING → JUDGING
  - Learning stage collects 1000 valid frames to generate threshold_matrix
  - Automatic reconstruction when environment changes (2000 consecutive problem frames)
  - Maintains high-performance judging code unchanged for optimal runtime performance
  - Uses pre_threshold matrix for initial learning phase validation

* **Maximum Memory Optimization Pattern**: Single matrix multiple-use strategy for embedded systems
  - Single library-declared matrix serves all purposes: max_abs_diff → threshold_matrix → final_thresholds
  - Temporal separation ensures no conflicts across learning and judging stages
  - Pointer-based architecture: pre_threshold (external), pixel_thresholds (library-declared mutable)
  - In-place transformations eliminate memory copies and intermediate storage
  - Achieves maximum memory efficiency: ~6KB total reduction, struct size reduced by ~6KB
  - Naming convention: pre_pixel_thresholds.h (external) vs pixel_thresholds (library memory)

* **ESP32-S3 SPIRAM Optimization Pattern**: Large memory allocation strategy for multi-sensor systems
  - Use `heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT)` for large data buffers
  - Keep small control structures in DRAM, move large arrays (>1KB) to SPIRAM
  - Dynamic allocation at system initialization, cleanup at shutdown
  - Typical usage: temperature buffers (~12KB), thermal detector instances (~280KB)
  - Reduces DRAM usage from overflow to ~75%, enables complex multi-sensor applications

## Architectural Patterns

* **Layered Initialization Pattern**: Clear separation between system-level and instance-level initialization
  - System-level initialization: Hardware infrastructure setup (I2C buses, basic sensor detection)
  - Instance-level initialization: Device-specific configuration (EEPROM, parameters, modes)
  - Eliminates functional overlap and improves maintainability
  - Enables independent testing and debugging of each layer
  - Follows separation of concerns principle for embedded systems

* **Multi-Sensor Quad-I2C Architecture Pattern**: Scalable sensor system design for ESP32 with 4 independent I2C buses
  - Core 0: Data acquisition tasks (I2C communication, sensor reading)
  - Core 1: Data processing tasks (thermal detection, anomaly analysis)
  - FreeRTOS queues for inter-core communication and synchronization
  - Static memory allocation with SPIRAM optimization for large datasets
  - 4 independent I2C buses: 2 hardware (I2C_NUM_0, I2C_NUM_1) + 2 software (GPIO-based)
  - One-to-one sensor mapping eliminates GPIO switching complexity and timing issues
  - Software I2C implementation provides flexible GPIO pin assignment for additional buses

## Testing Patterns

*   