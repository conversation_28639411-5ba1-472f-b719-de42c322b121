# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-01 15:12:55 - Log of updates made.

*

## Decision

* **ESP32 Multi-Sensor Initialization Responsibility Refactoring Strategy** - [2025-07-23 12:05:40]
  - 简化系统级硬件初始化（multi_sensor_hardware_init），只负责I2C总线初始化和基础传感器检测
  - 明确实例级初始化（mlx90640_instance_init）负责MLX90640特定配置（EEPROM、刷新率、模式设置）
  - 消除重复的传感器检测和硬件配置步骤，提高初始化效率
  - 更新函数注释和文档，明确系统级与实例级初始化的职责边界

* **ESP32 I2C Architecture Upgrade Strategy** - [2025-07-22 18:23:13]
  - 从双I2C总线+GPIO切换方案升级为4个独立I2C总线（2硬件+2软件）
  - 选择一对一传感器映射而不是继续使用GPIO多路复用，提高系统稳定性和简化设计
  - 实现软件I2C驱动以支持额外的I2C通道，而不是使用I2C扩展器或其他硬件方案
  - 保持与现有热检测算法和双核架构的完全兼容性

* **Multi-Sensor Compilation Error Fix Strategy** - [2025-07-22 18:23:13]
  - 选择修改代码使用正确的结构体字段访问，而不是修改结构体定义添加重复字段
  - 使用 `memset` 初始化整个 `task_stats_t` 结构体，而不是逐个字段初始化
  - 在CMakeLists.txt中添加缺失的软件I2C实现文件，确保链接完整性

* **Learning Stage Sliding Window Clearing Strategy** - [2025-07-12 14:59:41]
  - Clear sliding window when consecutive problem frames exceed RESTART_LRN threshold
  - Preserve learning progress (collected_frames and max_abs_diff) while resetting window state
  - Use existing init_window_fill() function to ensure complete window state reset

* **Frame Count Increment Bug Fix Strategy** - [2025-07-12 14:23:58]
  - Remove frame count increment from `thermal_detector_process_frame()`
  - Keep centralized frame counting in `thermal_detector_process_frame_with_learning()`
  - Add frame counting to `thermal_detector_process_frame_1d()` for 1D interface compatibility

## Rationale

* **ESP32 Multi-Sensor Initialization Responsibility Refactoring** - 原始设计中multi_sensor_hardware_init()和mlx90640_instance_init()存在功能重叠，特别是传感器检测和硬件配置被重复执行。通过明确职责划分，系统级初始化专注于硬件基础设施建立，实例级初始化专注于传感器特定功能配置。这种分离提高了代码的可维护性，减少了重复工作，并使初始化流程更加清晰。重构后的架构更符合分层设计原则，便于调试和扩展。

* **ESP32 I2C Architecture Upgrade** - The original dual I2C + GPIO switching design required complex sensor multiplexing logic and could introduce timing issues and potential conflicts. The upgraded 4 independent I2C buses approach eliminates these complexities by providing each sensor with a dedicated communication channel. Software I2C implementation allows flexibility in GPIO pin assignment while maintaining compatibility with existing hardware I2C performance. This architecture scales better and provides more reliable sensor communication, especially important for industrial thermal detection applications.

* **Multi-Sensor Compilation Error Fix** - The compilation errors were caused by inconsistent data structure usage and missing implementation files. The chosen approach maintains code consistency by using the existing `task_stats_t` structure design rather than adding duplicate fields. Using `memset` for initialization is more efficient and less error-prone than individual field assignments. Adding the missing software I2C file to CMakeLists.txt ensures all 4 I2C buses (2 hardware + 2 software) are properly linked and functional.

* **Learning Stage Sliding Window Clearing** - When consecutive problem frames exceed RESTART_LRN threshold (10 frames), the original code only reset the consecutive counter but left the sliding window polluted with potentially problematic historical data. This could affect subsequent learning quality and baseline establishment. The fix ensures clean sliding window data while preserving valuable learning progress, improving system robustness in changing environments.

* **Frame Count Increment Bug Fix** - The judging stage was experiencing double frame count increments because both the state machine function (`thermal_detector_process_frame_with_learning`) and the core processing function (`thermal_detector_process_frame`) were incrementing the counter. This caused frame numbers to advance by 2 instead of 1 in judging stage, breaking frame synchronization and even/odd frame logic. The chosen solution centralizes frame counting in the state machine function while maintaining backward compatibility for direct API usage.

## Implementation Details

* **Multi-Sensor Initialization Responsibility Refactoring Implementation**:
  - **系统级简化**: 将multi_sensor_hardware_init()从5个步骤简化为3个步骤，移除重复的硬件配置和验证步骤
  - **函数职责明确**: multi_sensor_init_single_sensor()只做基础检测，移除MLX90640特定配置的TODO注释
  - **实例级优化**: mlx90640_instance_init_hardware()跳过重复的传感器检测，直接进行MLX90640特定配置
  - **注释更新**: 更新所有相关函数的注释，明确说明系统级与实例级的职责边界
  - **代码清理**: 移除init_step_names数组中的冗余步骤，保持日志输出的一致性
  - **Impact**: 消除了功能重叠，提高了初始化效率，使代码架构更加清晰和可维护

* **Multi-Sensor Compilation Error Fix Implementation**:
  - **Root Cause**: `multi_sensor_tasks.c` 第123-128行试图访问不存在的结构体字段（如 `instance->acquire_total_iterations`），实际应使用 `instance->acquire_stats.total_iterations`
  - **Solution**: 将错误的直接字段访问替换为 `memset(&instance->acquire_stats, 0, sizeof(task_stats_t))` 和 `memset(&instance->process_stats, 0, sizeof(task_stats_t))`
  - **Missing File**: `main/CMakeLists.txt` 缺少 `../mlx90640_multi/mlx90640_software_i2c.c` 文件，导致软件I2C函数链接失败
  - **Fix**: 在CMakeLists.txt的SRCS列表中添加软件I2C实现文件
  - **Impact**: 解决了编译错误，确保4个I2C总线（2硬件+2软件）的完整功能实现

* **Learning Stage Sliding Window Clearing Implementation**:
  - **Root Cause**: In `noise_learning_process_frame()`, when `consecutive_problem_frames >= RESTART_LRN`, only the counter was reset but sliding window state remained polluted
  - **Solution**: Added `init_window_fill(detector)` call to clear all sliding window state (buffer_index, frames_stored, pixel_history_sum, pixel_history_mean)
  - **Preservation**: Kept `collected_frames` and `max_abs_diff` (stored in `pixel_thresholds`) to maintain learning progress
  - **Impact**: Ensures clean baseline establishment after environmental disturbances while preserving valuable learning data

* **Frame Count Bug Fix Implementation**:
  - **Root Cause**: Double increment in judging stage: `thermal_detector_process_frame_with_learning()` → `process_judging_stage()` → `thermal_detector_process_frame()` both incremented `detector->frame_count`
  - **Solution**: Removed `detector->frame_count++` from `thermal_detector_process_frame()` (line 133)
  - **Compatibility**: Added `detector->frame_count++` to `thermal_detector_process_frame_1d()` to maintain 1D interface behavior
  - **Testing**: Created comprehensive test suite (`test_frame_count_fix.c`) verifying all processing paths
  - **Impact**: Ensures consistent frame counting across all states (WARMUP, LEARNING, JUDGING) while preserving existing functionality