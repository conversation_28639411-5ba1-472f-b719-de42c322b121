/**
 * @file mlx90640_instance.h
 * @brief Single MLX90640 sensor instance management
 * 
 * This file defines the structure and functions for managing individual
 * MLX90640 sensor instances within the multi-sensor system.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MLX90640_INSTANCE_H
#define MLX90640_INSTANCE_H

#include <stdint.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_err.h"
#include "mlx90640_multi_config.h"
#include "../thermalDetection/thermal_anomaly_core.h"
#include "../mlx90640/MLX90640_API.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// Forward Declarations
// ============================================================================

// Forward declaration to avoid circular dependency
struct task_stats_s;

/**
 * @brief Enhanced task statistics for 4-I2C architecture
 */
typedef struct task_stats_s {
    uint32_t total_iterations;      ///< Total task iterations
    uint32_t successful_iterations; ///< Successful iterations
    uint32_t error_count;           ///< Error counter
    uint32_t max_execution_time_us; ///< Maximum execution time
    uint32_t avg_execution_time_us; ///< Average execution time
    uint32_t last_execution_time_us;///< Last execution time
    uint32_t i2c_error_count;       ///< I2C specific error count
    uint32_t timeout_count;         ///< Timeout error count
    uint32_t recovery_count;        ///< Task recovery count
    float    success_rate;          ///< Success rate percentage
} task_stats_t;

// ============================================================================
// Data Structures
// ============================================================================

/**
 * @brief Single MLX90640 sensor instance
 * 
 * This structure contains all data and resources needed to manage
 * a single MLX90640 sensor, including hardware configuration,
 * data buffers, thermal detector, and synchronization primitives.
 */
typedef struct {
    // Hardware Configuration
    sensor_id_t sensor_id;              ///< Sensor identifier
    i2c_bus_type_t bus_type;            ///< I2C bus type (hardware or software)
    uint8_t device_addr;                ///< I2C device address
    
    // Data Buffers (statically allocated)
    float* temp_data;                   ///< Pointer to temperature data buffer (768 floats)
    
    // Thermal Detection
    thermal_detector_t* detector;       ///< Pointer to thermal detector instance
    
    // Task Synchronization
    QueueHandle_t data_queue;           ///< Data notification queue
    SemaphoreHandle_t data_mutex;       ///< Data buffer protection mutex
    
    // Task Handles
    TaskHandle_t acquire_task;          ///< Data acquisition task handle
    TaskHandle_t process_task;          ///< Data processing task handle
    
    // Status and Statistics
    bool is_initialized;                ///< Initialization status
    bool is_active;                     ///< Active status
    uint32_t frame_count;               ///< Total frames processed
    uint32_t error_count;               ///< Error counter
    uint32_t last_process_time_us;      ///< Last processing time in microseconds

    // Task Statistics - using task_stats_t for comprehensive statistics
    task_stats_t acquire_stats;         ///< Acquisition task statistics
    task_stats_t process_stats;         ///< Processing task statistics
    
    // MLX90640 Specific Data
    uint16_t mlx90640_frame[834];       ///< MLX90640 frame data buffer
    paramsMLX90640 mlx90640_params;     ///< MLX90640 parameters
    
} mlx90640_instance_t;

/**
 * @brief Instance statistics
 */
typedef struct {
    uint32_t total_frames;              ///< Total frames processed
    uint32_t error_frames;              ///< Frames with errors
    uint32_t filtered_frames;           ///< Pre-filtered frames
    uint32_t anomaly_frames;            ///< Frames with anomalies
    uint32_t trigger_frames;            ///< Frames that triggered alerts
    float avg_process_time_us;          ///< Average processing time
    float max_process_time_us;          ///< Maximum processing time
} instance_stats_t;

// ============================================================================
// Instance Management Functions
// ============================================================================

/**
 * @brief Initialize a sensor instance
 * 
 * @param instance Pointer to instance structure
 * @param sensor_id Sensor identifier
 * @param temp_data Pointer to temperature data buffer
 * @param detector Pointer to thermal detector
 * @param config Thermal detection configuration
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_init(mlx90640_instance_t* instance,
                                sensor_id_t sensor_id,
                                float* temp_data,
                                thermal_detector_t* detector,
                                const thermal_config_t* config);

/**
 * @brief Deinitialize a sensor instance
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_deinit(mlx90640_instance_t* instance);

/**
 * @brief Start instance tasks
 * 
 * Creates and starts the acquisition and processing tasks for this instance.
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_start_tasks(mlx90640_instance_t* instance);

/**
 * @brief Stop instance tasks
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_stop_tasks(mlx90640_instance_t* instance);

/**
 * @brief Check if instance is healthy
 * 
 * @param instance Pointer to instance structure
 * @return true if healthy, false otherwise
 */
bool mlx90640_instance_is_healthy(const mlx90640_instance_t* instance);

// ============================================================================
// Data Access Functions
// ============================================================================

/**
 * @brief Get instance statistics
 * 
 * @param instance Pointer to instance structure
 * @param stats Output statistics structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_get_stats(const mlx90640_instance_t* instance, instance_stats_t* stats);

/**
 * @brief Reset instance statistics
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_reset_stats(mlx90640_instance_t* instance);

/**
 * @brief Get current temperature data (thread-safe)
 * 
 * @param instance Pointer to instance structure
 * @param temp_data Output buffer for temperature data
 * @param timeout_ms Timeout in milliseconds
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_get_temp_data(mlx90640_instance_t* instance, 
                                         float* temp_data, 
                                         uint32_t timeout_ms);

// ============================================================================
// Hardware Interface Functions
// ============================================================================

/**
 * @brief Read frame data from MLX90640 sensor
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_read_frame(mlx90640_instance_t* instance);

/**
 * @brief Convert frame data to temperature values
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_calculate_temperatures(mlx90640_instance_t* instance);

/**
 * @brief Initialize MLX90640 hardware for this instance
 * 
 * @param instance Pointer to instance structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_instance_init_hardware(mlx90640_instance_t* instance);

// ============================================================================
// Task Functions (Internal)
// ============================================================================

/**
 * @brief Data acquisition task function
 * 
 * This task runs on Core 0 and is responsible for reading data from
 * the MLX90640 sensor and notifying the processing task.
 * 
 * @param param Pointer to mlx90640_instance_t
 */
void mlx90640_acquire_task(void* param);

/**
 * @brief Data processing task function
 * 
 * This task runs on Core 1 and is responsible for processing thermal
 * data and performing anomaly detection.
 * 
 * @param param Pointer to mlx90640_instance_t
 */
void mlx90640_process_task(void* param);

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * @brief Get instance name string for logging
 * 
 * @param instance Pointer to instance structure
 * @return String representation of instance name
 */
const char* mlx90640_instance_get_name(const mlx90640_instance_t* instance);

/**
 * @brief Log instance status
 * 
 * @param instance Pointer to instance structure
 */
void mlx90640_instance_log_status(const mlx90640_instance_t* instance);

/**
 * @brief Validate instance structure
 * 
 * @param instance Pointer to instance structure
 * @return true if valid, false otherwise
 */
bool mlx90640_instance_validate(const mlx90640_instance_t* instance);

#ifdef __cplusplus
}
#endif

#endif // MLX90640_INSTANCE_H
