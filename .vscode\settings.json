{
  "C_Cpp.intelliSenseEngine": "default",
  "idf.espIdfPathWin": "d:\\ProgramFiles\\Espressif\\frameworks\\esp-idf-v5.4.2",
  "idf.toolsPathWin": "d:\\ProgramFiles\\Espressif\\tools",
  "idf.pythonInstallPath": "d:\\ProgramFiles\\Espressif\\tools\\tools\\idf-python\\3.11.2\\python.exe",
  "idf.customExtraVars": {
    "IDF_TARGET": "esp32s3"
  },
  "idf.openOcdConfigs": [
    "board/esp32s3-builtin.cfg"
  ],
  "terminal.integrated.profiles.windows": {
    "Command Prompt": {
      "path": [
        "C:\\Windows\\System32\\cmd.exe"
      ],
      "args": [
        "/k D:\\Document\\Code\\esp32_LEARN\\examples\\get-started\\blink2\\blink\\init.bat"
      ],
      "icon": "terminal-cmd"
    },
  },
  "idf.portWin": "COM20",
  "idf.flashType": "UART",
  "files.associations": {
    "*.tsp": "lua",
    "sdkconfig.h": "c",
    "task.h": "c",
    "freertos.h": "c",
    "esp_log.h": "c",
    "thermal_anomaly_core.h": "c",
    "nvs_flash.h": "c"
  }
}
