/**
 * @file mlx90640_software_i2c.h
 * @brief Software I2C driver for MLX90640 sensors
 * 
 * This driver provides software I2C implementation for ESP32 to support
 * additional MLX90640 sensors beyond the hardware I2C buses.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MLX90640_SOFTWARE_I2C_H
#define MLX90640_SOFTWARE_I2C_H

#include <stdint.h>
#include <stdbool.h>
#include "driver/gpio.h"
#include "esp_err.h"
#include "mlx90640_multi_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// Software I2C Configuration
// ============================================================================

// Software I2C timing parameters (microseconds) for 400kHz operation
// SW_I2C_DELAY_US is defined in mlx90640_multi_config.h to avoid conflicts
#define SW_I2C_SETUP_TIME_US    1      // Setup time before clock edge
#define SW_I2C_HOLD_TIME_US     1      // Hold time after clock edge
#define SW_I2C_TIMEOUT_US       10000  // Timeout for operations

// Software I2C instance structure
typedef struct {
    gpio_num_t sda_gpio;    // SDA GPIO pin
    gpio_num_t scl_gpio;    // SCL GPIO pin
    bool initialized;       // Initialization status
    uint32_t frequency;     // Target frequency in Hz
} sw_i2c_config_t;

// ============================================================================
// Software I2C Functions
// ============================================================================

/**
 * @brief Initialize software I2C bus
 * 
 * @param bus_id Software I2C bus ID (I2C_BUS_SOFTWARE_2 or I2C_BUS_SOFTWARE_3)
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_init(i2c_bus_type_t bus_id);

/**
 * @brief Deinitialize software I2C bus
 * 
 * @param bus_id Software I2C bus ID
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_deinit(i2c_bus_type_t bus_id);

/**
 * @brief Write data to I2C device using software I2C
 * 
 * @param bus_id Software I2C bus ID
 * @param device_addr I2C device address (7-bit)
 * @param reg_addr Register address to write to
 * @param data Pointer to data buffer
 * @param len Length of data to write
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_write(i2c_bus_type_t bus_id, uint8_t device_addr, 
                       uint16_t reg_addr, const uint8_t* data, size_t len);

/**
 * @brief Read data from I2C device using software I2C
 * 
 * @param bus_id Software I2C bus ID
 * @param device_addr I2C device address (7-bit)
 * @param reg_addr Register address to read from
 * @param data Pointer to data buffer
 * @param len Length of data to read
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_read(i2c_bus_type_t bus_id, uint8_t device_addr,
                      uint16_t reg_addr, uint8_t* data, size_t len);

/**
 * @brief Check if I2C device is present on software I2C bus
 * 
 * @param bus_id Software I2C bus ID
 * @param device_addr I2C device address (7-bit)
 * @return true if device responds, false otherwise
 */
bool sw_i2c_device_present(i2c_bus_type_t bus_id, uint8_t device_addr);

// ============================================================================
// Low-level Software I2C Functions
// ============================================================================

/**
 * @brief Generate I2C start condition
 * 
 * @param config Software I2C configuration
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_start(const sw_i2c_config_t* config);

/**
 * @brief Generate I2C stop condition
 * 
 * @param config Software I2C configuration
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_stop(const sw_i2c_config_t* config);

/**
 * @brief Write one byte to I2C bus
 * 
 * @param config Software I2C configuration
 * @param byte Byte to write
 * @return ESP_OK if ACK received, ESP_ERR_TIMEOUT if NACK
 */
esp_err_t sw_i2c_write_byte(const sw_i2c_config_t* config, uint8_t byte);

/**
 * @brief Read one byte from I2C bus
 * 
 * @param config Software I2C configuration
 * @param byte Pointer to store read byte
 * @param ack true to send ACK, false to send NACK
 * @return ESP_OK on success, error code on failure
 */
esp_err_t sw_i2c_read_byte(const sw_i2c_config_t* config, uint8_t* byte, bool ack);

#ifdef __cplusplus
}
#endif

#endif // MLX90640_SOFTWARE_I2C_H
