/**
 * @file mlx90640_i2c_multi.h
 * @brief Extended I2C driver for multiple MLX90640 sensors
 * 
 * This file extends the original MLX90640 I2C driver to support:
 * - Dual I2C bus operation (I2C_NUM_0 and I2C_NUM_1)
 * - Multiple sensor address management
 * - Sensor address modification
 * - GPIO-controlled analog switches
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MLX90640_I2C_MULTI_H
#define MLX90640_I2C_MULTI_H

#include <stdint.h>
#include <stdbool.h>
#include "driver/i2c.h"
#include "driver/gpio.h"
#include "esp_err.h"
#include "mlx90640_multi_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// I2C Multi-Bus Functions
// ============================================================================

/**
 * @brief Initialize all 4 I2C buses for multi-sensor operation
 *
 * Initializes 2 hardware I2C buses (I2C_NUM_0, I2C_NUM_1) and
 * 2 software I2C buses with the configured GPIO pins.
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_i2c_multi_init(void);

/**
 * @brief Deinitialize both I2C buses
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_i2c_multi_deinit(void);

/**
 * @brief Read data from MLX90640 on specified I2C bus
 * 
 * @param i2c_port I2C port number (0 or 1)
 * @param slave_addr Slave address (0x33 or 0x34)
 * @param start_address Starting register address
 * @param n_mem_address_read Number of 16-bit words to read
 * @param data Pointer to data buffer
 * @return 0 on success, -1 on failure
 */
int mlx90640_i2c_multi_read(uint8_t i2c_port, uint8_t slave_addr, 
                           uint16_t start_address, uint16_t n_mem_address_read, 
                           uint16_t *data);

/**
 * @brief Write data to MLX90640 on specified I2C bus
 * 
 * @param i2c_port I2C port number (0 or 1)
 * @param slave_addr Slave address (0x33 or 0x34)
 * @param write_address Register address to write
 * @param data Data to write
 * @return 0 on success, -1 on failure
 */
int mlx90640_i2c_multi_write(uint8_t i2c_port, uint8_t slave_addr,
                            uint16_t write_address, uint16_t data);

// ============================================================================
// Sensor Management Functions
// ============================================================================

/**
 * @brief Check if sensor is present on specified I2C bus and address
 * 
 * @param i2c_port I2C port number (0 or 1)
 * @param slave_addr Slave address to check
 * @return true if sensor responds, false otherwise
 */
bool mlx90640_sensor_detect(uint8_t i2c_port, uint8_t slave_addr);

/**
 * @brief Change MLX90640 sensor address
 * 
 * This function changes the I2C address of an MLX90640 sensor from
 * old_addr to new_addr on the specified I2C bus.
 * 
 * @param i2c_port I2C port number (0 or 1)
 * @param old_addr Current sensor address
 * @param new_addr New sensor address to set
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_change_sensor_address(uint8_t i2c_port, uint8_t old_addr, uint8_t new_addr);

/**
 * @brief Initialize a single MLX90640 sensor
 *
 * @param i2c_port I2C port number (0 or 1)
 * @param slave_addr Sensor address
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_init_sensor(uint8_t i2c_port, uint8_t slave_addr);

// ============================================================================
// Unified I2C Interface Functions (Hardware + Software)
// ============================================================================

/**
 * @brief Unified I2C read function for all 4 buses
 *
 * @param bus_type I2C bus type (hardware or software)
 * @param slave_addr Slave device address
 * @param start_address Starting register address
 * @param n_mem_address_read Number of 16-bit words to read
 * @param data Pointer to data buffer
 * @return 0 on success, -1 on failure
 */
int mlx90640_i2c_unified_read(i2c_bus_type_t bus_type, uint8_t slave_addr,
                              uint16_t start_address, uint16_t n_mem_address_read,
                              uint16_t *data);

/**
 * @brief Unified I2C write function for all 4 buses
 *
 * @param bus_type I2C bus type (hardware or software)
 * @param slave_addr Slave device address
 * @param write_address Register address to write
 * @param data Data to write
 * @return 0 on success, -1 on failure
 */
int mlx90640_i2c_unified_write(i2c_bus_type_t bus_type, uint8_t slave_addr,
                               uint16_t write_address, uint16_t data);

/**
 * @brief Check if sensor is present on unified I2C interface
 *
 * @param bus_type I2C bus type (hardware or software)
 * @param slave_addr Slave address to check
 * @return true if sensor responds, false otherwise
 */
bool mlx90640_sensor_detect_unified(i2c_bus_type_t bus_type, uint8_t slave_addr);

// ============================================================================
// GPIO Control Functions (DEPRECATED - Not needed in 4-I2C bus architecture)
// ============================================================================

/**
 * @brief Initialize GPIO pins for analog switch control
 * @deprecated No longer needed in 4-I2C bus architecture
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_gpio_init(void) __attribute__((deprecated));

/**
 * @brief Control analog switch state
 * @deprecated No longer needed in 4-I2C bus architecture
 * @param switch_gpio GPIO pin number
 * @param enable true to enable (high), false to disable (low)
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_switch_control(gpio_num_t switch_gpio, bool enable) __attribute__((deprecated));

/**
 * @brief Enable/disable sensor power via analog switch
 * @deprecated No longer needed in 4-I2C bus architecture
 * @param sensor_id Sensor ID
 * @param enable true to enable power, false to disable
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_sensor_power_control(sensor_id_t sensor_id, bool enable) __attribute__((deprecated));

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * @brief Get I2C bus name string for logging
 *
 * @param bus_type I2C bus type
 * @return String representation of I2C bus
 */
const char* mlx90640_get_i2c_bus_name(i2c_bus_type_t bus_type);

/**
 * @brief Get sensor name string for logging
 *
 * @param sensor_id Sensor ID
 * @return String representation of sensor name
 */
const char* mlx90640_get_sensor_name(sensor_id_t sensor_id);

/**
 * @brief Convert sensor ID to I2C parameters
 *
 * @param sensor_id Sensor ID
 * @param bus_type Output: I2C bus type
 * @param slave_addr Output: Slave address
 * @return ESP_OK on success, ESP_ERR_INVALID_ARG on invalid sensor_id
 */
esp_err_t mlx90640_sensor_to_i2c_params(sensor_id_t sensor_id, i2c_bus_type_t* bus_type, uint8_t* slave_addr);

// ============================================================================
// Temperature Reading Functions
// ============================================================================

/**
 * @brief Read temperature data from specific sensor
 * 
 * @param sensor_id Sensor ID
 * @param temp_data Output buffer for temperature data (768 floats)
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_read_temperature_multi(sensor_id_t sensor_id, float* temp_data);

#ifdef __cplusplus
}
#endif

#endif // MLX90640_I2C_MULTI_H
