/**
 * @file multi_sensor_tasks.c
 * @brief Multi-sensor dual-core task system implementation
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include "multi_sensor_tasks.h"
#include "mlx90640_i2c_multi.h"
#include "mlx90640_multi_manager.h"
#include "MLX90640_API.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "MULTI_SENSOR_TASKS";

// ============================================================================
// Task Management Functions
// ============================================================================

esp_err_t multi_sensor_create_all_tasks(void)
{
    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();
    esp_err_t ret;
    
    MULTI_SENSOR_LOGI(TAG, "Creating tasks for all sensors");
    
    for (int i = 0; i < SENSOR_COUNT; i++) {
        ret = multi_sensor_create_sensor_tasks(&manager->sensors[i]);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to create tasks for sensor %s: %s",
                             mlx90640_get_sensor_name((sensor_id_t)i), esp_err_to_name(ret));
            // Clean up already created tasks
            for (int j = 0; j < i; j++) {
                multi_sensor_delete_sensor_tasks(&manager->sensors[j]);
            }
            return ret;
        }
    }
    
    MULTI_SENSOR_LOGI(TAG, "All sensor tasks created successfully");
    return ESP_OK;
}

esp_err_t multi_sensor_delete_all_tasks(void)
{
    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();
    
    MULTI_SENSOR_LOGI(TAG, "Deleting all sensor tasks");
    
    for (int i = 0; i < SENSOR_COUNT; i++) {
        multi_sensor_delete_sensor_tasks(&manager->sensors[i]);
    }
    
    MULTI_SENSOR_LOGI(TAG, "All sensor tasks deleted");
    return ESP_OK;
}

esp_err_t multi_sensor_create_sensor_tasks(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    const char* sensor_name = mlx90640_get_sensor_name(instance->sensor_id);
    char task_name[32];
    BaseType_t ret;

    // Determine I2C type and configure accordingly
    bool is_software_i2c = SENSOR_IS_SOFTWARE_I2C(instance->sensor_id);
    uint32_t acquire_stack = is_software_i2c ? ACQUIRE_TASK_STACK_SW : ACQUIRE_TASK_STACK_HW;
    UBaseType_t acquire_priority = is_software_i2c ? ACQUIRE_TASK_PRIORITY_SW : ACQUIRE_TASK_PRIORITY_HW;

    MULTI_SENSOR_LOGI(TAG, "Creating tasks for sensor %s (%s I2C)",
                     sensor_name, is_software_i2c ? "Software" : "Hardware");
    MULTI_SENSOR_LOGI(TAG, "  Acquisition: Stack=%u, Priority=%u",
                     (unsigned)acquire_stack, (unsigned)acquire_priority);

    // Create acquisition task (Core 0) with I2C-specific configuration
    snprintf(task_name, sizeof(task_name), ACQUIRE_TASK_NAME_FMT, sensor_name);
    ret = xTaskCreatePinnedToCore(
        multi_sensor_acquire_task,      // Task function
        task_name,                      // Task name
        acquire_stack,                  // Stack size (I2C type specific)
        (void*)instance,                // Task parameter
        acquire_priority,               // Priority (I2C type specific)
        &instance->acquire_task,        // Task handle
        ACQUIRE_TASK_CORE               // Core ID
    );

    if (ret != pdPASS) {
        MULTI_SENSOR_LOGE(TAG, "Failed to create acquisition task for %s", sensor_name);
        return ESP_ERR_NO_MEM;
    }
    
    // Create processing task (Core 1) with staggered start time
    snprintf(task_name, sizeof(task_name), PROCESS_TASK_NAME_FMT, sensor_name);
    ret = xTaskCreatePinnedToCore(
        multi_sensor_process_task,      // Task function
        task_name,                      // Task name
        PROCESS_TASK_STACK,             // Stack size
        (void*)instance,                // Task parameter
        PROCESS_TASK_PRIORITY,          // Priority (same for all processing tasks)
        &instance->process_task,        // Task handle
        PROCESS_TASK_CORE               // Core ID
    );

    if (ret != pdPASS) {
        MULTI_SENSOR_LOGE(TAG, "Failed to create processing task for %s", sensor_name);
        // Clean up acquisition task
        if (instance->acquire_task != NULL) {
            vTaskDelete(instance->acquire_task);
            instance->acquire_task = NULL;
        }
        return ESP_ERR_NO_MEM;
    }

    // Initialize task statistics counters
    memset(&instance->acquire_stats, 0, sizeof(task_stats_t));
    memset(&instance->process_stats, 0, sizeof(task_stats_t));

    MULTI_SENSOR_LOGI(TAG, "Tasks created successfully for sensor %s (%s I2C)",
                     sensor_name, is_software_i2c ? "Software" : "Hardware");
    return ESP_OK;
}

esp_err_t multi_sensor_delete_sensor_tasks(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    const char* sensor_name = mlx90640_get_sensor_name(instance->sensor_id);
    
    MULTI_SENSOR_LOGI(TAG, "Deleting tasks for sensor %s", sensor_name);
    
    // Send stop notification first
    multi_sensor_notify_stop(instance);
    
    // Wait a bit for tasks to stop gracefully
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // Delete acquisition task
    if (instance->acquire_task != NULL) {
        vTaskDelete(instance->acquire_task);
        instance->acquire_task = NULL;
    }
    
    // Delete processing task
    if (instance->process_task != NULL) {
        vTaskDelete(instance->process_task);
        instance->process_task = NULL;
    }
    
    MULTI_SENSOR_LOGI(TAG, "Tasks deleted for sensor %s", sensor_name);
    return ESP_OK;
}

// ============================================================================
// Core Task Functions
// ============================================================================

void multi_sensor_acquire_task(void* param)
{
    mlx90640_instance_t* instance = (mlx90640_instance_t*)param;
    const char* sensor_name = mlx90640_get_sensor_name(instance->sensor_id);
    uint32_t start_time, execution_time;
    esp_err_t ret;
    bool is_software_i2c = SENSOR_IS_SOFTWARE_I2C(instance->sensor_id);
    uint32_t consecutive_errors = 0;
    uint32_t stagger_delay = instance->sensor_id * FRAME_STAGGER_MS;

    MULTI_SENSOR_LOGI(TAG, "Acquisition task started for sensor %s (%s I2C, Core %d)",
                     sensor_name, is_software_i2c ? "Software" : "Hardware", xPortGetCoreID());

    // Stagger start times to balance load
    if (stagger_delay > 0) {
        MULTI_SENSOR_LOGD(TAG, "Staggering start for %s by %u ms", sensor_name, (unsigned)stagger_delay);
        vTaskDelay(pdMS_TO_TICKS(stagger_delay));
    }

    instance->is_active = true;

    while (instance->is_active) {
        start_time = esp_timer_get_time();
        bool success = false;

        // Read frame data from MLX90640 with retry for software I2C
        int retry_count = is_software_i2c ? SW_I2C_RETRY_COUNT : 1;
        for (int retry = 0; retry < retry_count && !success; retry++) {
            if (retry > 0 && is_software_i2c) {
                // Small delay between retries for software I2C
                vTaskDelay(pdMS_TO_TICKS(SW_I2C_DELAY_US / 1000));
            }

            ret = mlx90640_instance_read_frame(instance);
            if (ret == ESP_OK) {
                // Convert to temperature data
                ret = mlx90640_instance_calculate_temperatures(instance);
                if (ret == ESP_OK) {
                    // Notify processing task that data is ready
                    multi_sensor_notify_data_ready(instance);
                    instance->frame_count++;
                    consecutive_errors = 0;
                    success = true;
                } else {
                    MULTI_SENSOR_LOGW(TAG, "Temperature calculation failed for %s (retry %d): %s",
                                     sensor_name, retry, esp_err_to_name(ret));
                }
            } else {
                MULTI_SENSOR_LOGW(TAG, "Frame read failed for %s (retry %d): %s",
                                 sensor_name, retry, esp_err_to_name(ret));
            }
        }

        if (!success) {
            instance->error_count++;
            consecutive_errors++;

            // Check if we need task recovery for software I2C
            if (is_software_i2c && consecutive_errors >= MAX_SW_I2C_ERRORS) {
                MULTI_SENSOR_LOGE(TAG, "Too many consecutive errors for %s, requesting recovery", sensor_name);
                // TODO: Implement task recovery mechanism
                consecutive_errors = 0;
            }
        }

        // Update timing statistics
        execution_time = esp_timer_get_time() - start_time;
        instance->last_process_time_us = execution_time;

        // Update enhanced statistics
        multi_sensor_update_task_stats(&instance->acquire_stats, execution_time, success);

        // Control frame rate (~10 FPS)
        vTaskDelay(pdMS_TO_TICKS(FRAME_INTERVAL_MS));
    }

    MULTI_SENSOR_LOGI(TAG, "Acquisition task stopped for sensor %s", sensor_name);
    vTaskDelete(NULL);
}

void multi_sensor_process_task(void* param)
{
    mlx90640_instance_t* instance = (mlx90640_instance_t*)param;
    const char* sensor_name = mlx90640_get_sensor_name(instance->sensor_id);
    uint32_t start_time, execution_time;
    thermal_result_t result;
    int detection_ret;
    bool is_software_i2c = SENSOR_IS_SOFTWARE_I2C(instance->sensor_id);

    MULTI_SENSOR_LOGI(TAG, "Processing task started for sensor %s (%s I2C, Core %d)",
                     sensor_name, is_software_i2c ? "Software" : "Hardware", xPortGetCoreID());

    while (instance->is_active) {
        // Wait for data ready notification with timeout
        esp_err_t ret = multi_sensor_wait_data_ready(instance, DATA_READY_TIMEOUT_MS);
        if (ret != ESP_OK) {
            if (ret == ESP_ERR_TIMEOUT) {
                instance->process_stats.timeout_count++;
                MULTI_SENSOR_LOGW(TAG, "Data ready timeout for %s", sensor_name);
            }
            continue;
        }

        start_time = esp_timer_get_time();
        bool success = false;

        // Acquire data mutex for thread-safe access with timeout
        if (xSemaphoreTake(instance->data_mutex, pdMS_TO_TICKS(MUTEX_TIMEOUT_MS)) == pdTRUE) {

            // Perform thermal anomaly detection with learning
            detection_ret = thermal_detector_process_frame_1d_with_learning(
                instance->detector,
                instance->temp_data,
                &result
            );

            // Release data mutex
            xSemaphoreGive(instance->data_mutex);

            // Process detection results
            if (detection_ret == 0) {
                success = true;

                // Log detection results
                MULTI_SENSOR_LOGD(TAG, "Sensor %s: State=%d, Frame=%u, Anomalies=%u",
                                 sensor_name,
                                 instance->detector->state,
                                 (unsigned)result.frame_count,
                                 (unsigned)result.anomaly_pixel_count);

                // Handle anomaly detection results
                if (result.anomaly_pixel_count > 0) {
                    MULTI_SENSOR_LOGI(TAG, "Sensor %s detected %u anomaly pixels at frame %u",
                                     sensor_name, (unsigned)result.anomaly_pixel_count,
                                     (unsigned)result.frame_count);

                    // TODO: Add UART output or other result processing here
                }
            } else {
                instance->error_count++;
                instance->process_stats.i2c_error_count++;
                MULTI_SENSOR_LOGW(TAG, "Thermal detection failed for %s: error %d",
                                 sensor_name, detection_ret);
            }

        } else {
            instance->error_count++;
            instance->process_stats.timeout_count++;
            MULTI_SENSOR_LOGW(TAG, "Failed to acquire data mutex for %s (timeout)", sensor_name);
        }

        // Update timing statistics
        execution_time = esp_timer_get_time() - start_time;
        instance->last_process_time_us = execution_time;

        // Update enhanced statistics
        multi_sensor_update_task_stats(&instance->process_stats, execution_time, success);
    }
    
    MULTI_SENSOR_LOGI(TAG, "Processing task stopped for sensor %s", sensor_name);
    vTaskDelete(NULL);
}

// ============================================================================
// Task Communication Functions
// ============================================================================

esp_err_t multi_sensor_notify_data_ready(mlx90640_instance_t* instance)
{
    if (instance == NULL || instance->data_queue == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    uint32_t notification = TASK_NOTIFY_DATA_READY;
    BaseType_t ret = xQueueSend(instance->data_queue, &notification, 0);
    
    return (ret == pdTRUE) ? ESP_OK : ESP_ERR_TIMEOUT;
}

esp_err_t multi_sensor_wait_data_ready(mlx90640_instance_t* instance, uint32_t timeout_ms)
{
    if (instance == NULL || instance->data_queue == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    uint32_t notification;
    TickType_t timeout_ticks = (timeout_ms == portMAX_DELAY) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
    
    BaseType_t ret = xQueueReceive(instance->data_queue, &notification, timeout_ticks);
    if (ret != pdTRUE) {
        return ESP_ERR_TIMEOUT;
    }
    
    if (notification & TASK_NOTIFY_STOP) {
        return ESP_ERR_INVALID_STATE;
    }
    
    return ESP_OK;
}

esp_err_t multi_sensor_notify_stop(mlx90640_instance_t* instance)
{
    if (instance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    instance->is_active = false;
    
    if (instance->data_queue != NULL) {
        uint32_t notification = TASK_NOTIFY_STOP;
        xQueueSend(instance->data_queue, &notification, 0);
    }
    
    return ESP_OK;
}

// ============================================================================
// Task Monitoring Functions
// ============================================================================

bool multi_sensor_all_tasks_healthy(void)
{
    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();

    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_t* instance = &manager->sensors[i];

        // Check if tasks exist and are running
        if (instance->acquire_task == NULL || instance->process_task == NULL) {
            return false;
        }

        // Check if instance is active
        if (!instance->is_active) {
            return false;
        }

        // Check error rate (more than 50% errors is unhealthy)
        if (instance->frame_count > 10 &&
            (instance->error_count * 2) > instance->frame_count) {
            return false;
        }
    }

    return true;
}

void multi_sensor_log_task_status(void)
{
    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();

    MULTI_SENSOR_LOGI(TAG, "=== Enhanced Task Status Summary ===");

    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_t* instance = &manager->sensors[i];
        const char* sensor_name = mlx90640_get_sensor_name((sensor_id_t)i);
        bool is_software_i2c = SENSOR_IS_SOFTWARE_I2C(i);

        MULTI_SENSOR_LOGI(TAG, "Sensor %s (%s I2C):", sensor_name,
                         is_software_i2c ? "Software" : "Hardware");
        MULTI_SENSOR_LOGI(TAG, "  Active: %s", instance->is_active ? "Yes" : "No");
        MULTI_SENSOR_LOGI(TAG, "  Frames: %u", (unsigned)instance->frame_count);
        MULTI_SENSOR_LOGI(TAG, "  Errors: %u", (unsigned)instance->error_count);
        MULTI_SENSOR_LOGI(TAG, "  Last Process Time: %u us", (unsigned)instance->last_process_time_us);

        // Enhanced acquisition task statistics
        MULTI_SENSOR_LOGI(TAG, "  Acquire Task: %s",
                         instance->acquire_task ? "Running" : "Stopped");
        if (instance->acquire_stats.total_iterations > 0) {
            MULTI_SENSOR_LOGI(TAG, "    Success Rate: %.1f%%", instance->acquire_stats.success_rate);
            MULTI_SENSOR_LOGI(TAG, "    Avg Time: %u us", (unsigned)instance->acquire_stats.avg_execution_time_us);
            MULTI_SENSOR_LOGI(TAG, "    Max Time: %u us", (unsigned)instance->acquire_stats.max_execution_time_us);
        }

        // Enhanced processing task statistics
        MULTI_SENSOR_LOGI(TAG, "  Process Task: %s",
                         instance->process_task ? "Running" : "Stopped");
        if (instance->process_stats.total_iterations > 0) {
            MULTI_SENSOR_LOGI(TAG, "    Success Rate: %.1f%%", instance->process_stats.success_rate);
            MULTI_SENSOR_LOGI(TAG, "    Avg Time: %u us", (unsigned)instance->process_stats.avg_execution_time_us);
            MULTI_SENSOR_LOGI(TAG, "    Timeouts: %u", (unsigned)instance->process_stats.timeout_count);
            MULTI_SENSOR_LOGI(TAG, "    I2C Errors: %u", (unsigned)instance->process_stats.i2c_error_count);
        }
    }

    // Log priority adjustment recommendation
    if (multi_sensor_sw_i2c_needs_priority_boost()) {
        MULTI_SENSOR_LOGW(TAG, "Recommendation: Software I2C tasks may benefit from priority boost");
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

const char* multi_sensor_get_task_state_name(task_state_t state)
{
    switch (state) {
        case TASK_STATE_STOPPED:  return "STOPPED";
        case TASK_STATE_STARTING: return "STARTING";
        case TASK_STATE_RUNNING:  return "RUNNING";
        case TASK_STATE_STOPPING: return "STOPPING";
        case TASK_STATE_ERROR:    return "ERROR";
        default:                  return "UNKNOWN";
    }
}

void multi_sensor_update_task_stats(task_stats_t* stats, uint32_t execution_time_us, bool success)
{
    if (stats == NULL) {
        return;
    }

    stats->total_iterations++;
    stats->last_execution_time_us = execution_time_us;

    if (success) {
        stats->successful_iterations++;
    } else {
        stats->error_count++;
    }

    // Update max execution time
    if (execution_time_us > stats->max_execution_time_us) {
        stats->max_execution_time_us = execution_time_us;
    }

    // Update average execution time (simple moving average)
    if (stats->total_iterations == 1) {
        stats->avg_execution_time_us = execution_time_us;
    } else {
        stats->avg_execution_time_us =
            (stats->avg_execution_time_us * (stats->total_iterations - 1) + execution_time_us) /
            stats->total_iterations;
    }

    // Calculate success rate
    if (stats->total_iterations > 0) {
        stats->success_rate = (float)stats->successful_iterations / stats->total_iterations * 100.0f;
    }
}

void multi_sensor_init_task_control(task_control_t* control, const char* task_name)
{
    if (control == NULL || task_name == NULL) {
        return;
    }

    memset(control, 0, sizeof(task_control_t));
    strncpy(control->task_name, task_name, sizeof(control->task_name) - 1);
    control->state = TASK_STATE_STOPPED;
    control->start_time = esp_timer_get_time() / 1000000;
}

// ============================================================================
// Performance Monitoring Functions
// ============================================================================

esp_err_t multi_sensor_get_performance_metrics(float* total_fps,
                                              uint32_t* avg_process_time_us,
                                              uint32_t* max_process_time_us)
{
    if (total_fps == NULL || avg_process_time_us == NULL || max_process_time_us == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();
    uint32_t total_frames = 0;
    uint32_t total_process_time = 0;
    uint32_t max_time = 0;
    uint32_t uptime_seconds = mlx90640_multi_get_uptime_seconds();

    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_t* instance = &manager->sensors[i];
        total_frames += instance->frame_count;
        total_process_time += instance->last_process_time_us;

        if (instance->last_process_time_us > max_time) {
            max_time = instance->last_process_time_us;
        }
    }

    // Calculate total FPS across all sensors
    if (uptime_seconds > 0) {
        *total_fps = (float)total_frames / uptime_seconds;
    } else {
        *total_fps = 0.0f;
    }

    // Calculate average processing time
    if (SENSOR_COUNT > 0) {
        *avg_process_time_us = total_process_time / SENSOR_COUNT;
    } else {
        *avg_process_time_us = 0;
    }

    *max_process_time_us = max_time;

    return ESP_OK;
}

void multi_sensor_log_performance_summary(void)
{
    float total_fps;
    uint32_t avg_process_time_us, max_process_time_us;

    esp_err_t ret = multi_sensor_get_performance_metrics(&total_fps,
                                                        &avg_process_time_us,
                                                        &max_process_time_us);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Failed to get performance metrics: %s", esp_err_to_name(ret));
        return;
    }

    MULTI_SENSOR_LOGI(TAG, "=== Performance Summary ===");
    MULTI_SENSOR_LOGI(TAG, "Total FPS: %.2f", total_fps);
    MULTI_SENSOR_LOGI(TAG, "Average processing time: %u us", (unsigned)avg_process_time_us);
    MULTI_SENSOR_LOGI(TAG, "Maximum processing time: %u us", (unsigned)max_process_time_us);
    MULTI_SENSOR_LOGI(TAG, "Tasks healthy: %s", multi_sensor_all_tasks_healthy() ? "Yes" : "No");

    // Log I2C bus specific performance
    multi_sensor_log_i2c_bus_performance();
}

esp_err_t multi_sensor_get_i2c_bus_metrics(float* hw_i2c_fps, float* sw_i2c_fps,
                                          uint32_t* hw_i2c_errors, uint32_t* sw_i2c_errors)
{
    if (hw_i2c_fps == NULL || sw_i2c_fps == NULL ||
        hw_i2c_errors == NULL || sw_i2c_errors == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();
    uint32_t hw_frames = 0, sw_frames = 0;
    uint32_t hw_errors = 0, sw_errors = 0;
    uint32_t uptime_seconds = mlx90640_multi_get_uptime_seconds();

    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_t* instance = &manager->sensors[i];

        if (SENSOR_IS_SOFTWARE_I2C(i)) {
            sw_frames += instance->frame_count;
            sw_errors += instance->error_count;
        } else {
            hw_frames += instance->frame_count;
            hw_errors += instance->error_count;
        }
    }

    // Calculate FPS for each I2C type
    if (uptime_seconds > 0) {
        *hw_i2c_fps = (float)hw_frames / uptime_seconds;
        *sw_i2c_fps = (float)sw_frames / uptime_seconds;
    } else {
        *hw_i2c_fps = 0.0f;
        *sw_i2c_fps = 0.0f;
    }

    *hw_i2c_errors = hw_errors;
    *sw_i2c_errors = sw_errors;

    return ESP_OK;
}

void multi_sensor_log_i2c_bus_performance(void)
{
    float hw_i2c_fps, sw_i2c_fps;
    uint32_t hw_i2c_errors, sw_i2c_errors;

    esp_err_t ret = multi_sensor_get_i2c_bus_metrics(&hw_i2c_fps, &sw_i2c_fps,
                                                    &hw_i2c_errors, &sw_i2c_errors);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Failed to get I2C bus metrics: %s", esp_err_to_name(ret));
        return;
    }

    MULTI_SENSOR_LOGI(TAG, "=== I2C Bus Performance ===");
    MULTI_SENSOR_LOGI(TAG, "Hardware I2C: %.2f FPS, %u errors", hw_i2c_fps, (unsigned)hw_i2c_errors);
    MULTI_SENSOR_LOGI(TAG, "Software I2C: %.2f FPS, %u errors", sw_i2c_fps, (unsigned)sw_i2c_errors);

    // Calculate error rates
    float hw_error_rate = (hw_i2c_fps > 0) ? (hw_i2c_errors / (hw_i2c_fps * mlx90640_multi_get_uptime_seconds())) * 100.0f : 0.0f;
    float sw_error_rate = (sw_i2c_fps > 0) ? (sw_i2c_errors / (sw_i2c_fps * mlx90640_multi_get_uptime_seconds())) * 100.0f : 0.0f;

    MULTI_SENSOR_LOGI(TAG, "Hardware I2C error rate: %.2f%%", hw_error_rate);
    MULTI_SENSOR_LOGI(TAG, "Software I2C error rate: %.2f%%", sw_error_rate);
}

bool multi_sensor_sw_i2c_needs_priority_boost(void)
{
    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();

    for (int i = 0; i < SENSOR_COUNT; i++) {
        if (SENSOR_IS_SOFTWARE_I2C(i)) {
            mlx90640_instance_t* instance = &manager->sensors[i];

            // Check if software I2C has high error rate
            if (instance->frame_count > 10) {
                float error_rate = (float)instance->error_count / instance->frame_count;
                if (error_rate > 0.1f) { // More than 10% error rate
                    return true;
                }
            }

            // Check if processing time is consistently high
            if (instance->last_process_time_us > 50000) { // More than 50ms
                return true;
            }
        }
    }

    return false;
}

esp_err_t multi_sensor_adjust_task_priorities(void)
{
    mlx90640_multi_manager_t* manager = mlx90640_multi_get_manager();
    bool needs_boost = multi_sensor_sw_i2c_needs_priority_boost();

    MULTI_SENSOR_LOGD(TAG, "Adjusting task priorities, SW I2C boost needed: %s",
                     needs_boost ? "Yes" : "No");

    for (int i = 0; i < SENSOR_COUNT; i++) {
        mlx90640_instance_t* instance = &manager->sensors[i];

        if (instance->acquire_task != NULL) {
            UBaseType_t new_priority;

            if (SENSOR_IS_SOFTWARE_I2C(i)) {
                // Boost software I2C priority if needed
                new_priority = needs_boost ? (ACQUIRE_TASK_PRIORITY_SW + 1) : ACQUIRE_TASK_PRIORITY_SW;
            } else {
                // Keep hardware I2C priority stable
                new_priority = ACQUIRE_TASK_PRIORITY_HW;
            }

            vTaskPrioritySet(instance->acquire_task, new_priority);
        }
    }

    return ESP_OK;
}
