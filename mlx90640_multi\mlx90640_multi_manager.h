/**
 * @file mlx90640_multi_manager.h
 * @brief Multi-sensor system manager for 4 MLX90640 sensors
 * 
 * This file defines the main management system for coordinating
 * 4 MLX90640 sensors with dual I2C buses and dual-core processing.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#ifndef MLX90640_MULTI_MANAGER_H
#define MLX90640_MULTI_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "mlx90640_multi_config.h"
#include "mlx90640_instance.h"
#include "../thermalDetection/thermal_anomaly_core.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// System State Definitions
// ============================================================================

/**
 * @brief Multi-sensor system state
 */
typedef enum {
    MULTI_SYSTEM_UNINITIALIZED = 0,    ///< System not initialized
    MULTI_SYSTEM_INITIALIZING,         ///< System initialization in progress
    MULTI_SYSTEM_READY,                ///< System ready but not running
    MULTI_SYSTEM_RUNNING,              ///< System running normally
    MULTI_SYSTEM_ERROR,                ///< System in error state
    MULTI_SYSTEM_STOPPING              ///< System stopping
} multi_system_state_t;

/**
 * @brief System statistics
 */
typedef struct {
    uint32_t total_frames_processed;    ///< Total frames from all sensors
    uint32_t total_anomalies_detected;  ///< Total anomalies from all sensors
    uint32_t total_errors;              ///< Total errors from all sensors
    uint32_t uptime_seconds;            ///< System uptime in seconds
    float avg_fps;                      ///< Average frames per second
    uint32_t memory_usage_bytes;        ///< Estimated memory usage
} multi_system_stats_t;

// ============================================================================
// Main Manager Structure
// ============================================================================

/**
 * @brief Multi-sensor system manager
 * 
 * This structure contains all resources and state information
 * for managing the 4-sensor MLX90640 system.
 */
typedef struct {
    // System State
    multi_system_state_t state;         ///< Current system state
    bool initialized;                   ///< Initialization flag
    uint32_t start_time;                ///< System start timestamp
    
    // Sensor Instances
    mlx90640_instance_t sensors[SENSOR_COUNT];  ///< Array of sensor instances
    
    // Configuration
    thermal_config_t config;            ///< Unified thermal detection configuration
    
    // System Statistics
    multi_system_stats_t stats;         ///< System-wide statistics
    
    // Error Handling
    uint32_t last_error_time;           ///< Last error timestamp
    esp_err_t last_error_code;          ///< Last error code
    char last_error_msg[128];           ///< Last error message
    
} mlx90640_multi_manager_t;

// ============================================================================
// Static Memory Allocation
// ============================================================================

/**
 * @brief Get the global multi-sensor manager instance
 * 
 * @return Pointer to the global manager instance
 */
mlx90640_multi_manager_t* mlx90640_multi_get_manager(void);

// ============================================================================
// System Management Functions
// ============================================================================

/**
 * @brief Initialize the multi-sensor system
 * 
 * This function performs complete system initialization including:
 * - GPIO setup for analog switches
 * - Dual I2C bus initialization
 * - Sensor address modification sequence
 * - Sensor instance initialization
 * - Task creation and startup
 * 
 * @param config Thermal detection configuration (NULL for default)
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_system_init(const thermal_config_t* config);

/**
 * @brief Start the multi-sensor system
 * 
 * Starts all sensor tasks and begins data acquisition and processing.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_system_start(void);

/**
 * @brief Stop the multi-sensor system
 * 
 * Stops all sensor tasks and puts the system in ready state.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_system_stop(void);

/**
 * @brief Shutdown the multi-sensor system
 * 
 * Performs complete system shutdown including task deletion,
 * resource cleanup, and hardware deinitialization.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_system_shutdown(void);

/**
 * @brief Get current system state
 * 
 * @return Current system state
 */
multi_system_state_t mlx90640_multi_get_system_state(void);

/**
 * @brief Check if system is healthy
 * 
 * @return true if all sensors are healthy, false otherwise
 */
bool mlx90640_multi_system_is_healthy(void);

// ============================================================================
// Sensor Access Functions
// ============================================================================

/**
 * @brief Get sensor instance by ID
 * 
 * @param sensor_id Sensor identifier
 * @return Pointer to sensor instance, NULL if invalid ID
 */
mlx90640_instance_t* mlx90640_multi_get_sensor(sensor_id_t sensor_id);

/**
 * @brief Get sensor count
 * 
 * @return Number of sensors in the system
 */
uint8_t mlx90640_multi_get_sensor_count(void);

/**
 * @brief Check if specific sensor is active
 * 
 * @param sensor_id Sensor identifier
 * @return true if sensor is active, false otherwise
 */
bool mlx90640_multi_is_sensor_active(sensor_id_t sensor_id);

// ============================================================================
// Statistics and Monitoring Functions
// ============================================================================

/**
 * @brief Get system statistics
 * 
 * @param stats Output statistics structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_get_stats(multi_system_stats_t* stats);

/**
 * @brief Reset system statistics
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_reset_stats(void);

/**
 * @brief Update system statistics
 * 
 * This function should be called periodically to update system statistics.
 * 
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_update_stats(void);

/**
 * @brief Log system status
 * 
 * Logs detailed system status including all sensor states.
 */
void mlx90640_multi_log_system_status(void);

// ============================================================================
// Configuration Functions
// ============================================================================

/**
 * @brief Get current system configuration
 * 
 * @param config Output configuration structure
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_get_config(thermal_config_t* config);

/**
 * @brief Update system configuration
 * 
 * Updates configuration for all sensors. System must be stopped first.
 * 
 * @param config New configuration
 * @return ESP_OK on success, error code on failure
 */
esp_err_t mlx90640_multi_update_config(const thermal_config_t* config);

// ============================================================================
// Error Handling Functions
// ============================================================================

/**
 * @brief Get last system error
 * 
 * @param error_code Output error code
 * @param error_msg Output error message buffer
 * @param msg_size Size of error message buffer
 * @return ESP_OK if error info retrieved, ESP_ERR_NOT_FOUND if no error
 */
esp_err_t mlx90640_multi_get_last_error(esp_err_t* error_code, char* error_msg, size_t msg_size);

/**
 * @brief Clear last system error
 * 
 * @return ESP_OK on success
 */
esp_err_t mlx90640_multi_clear_last_error(void);

/**
 * @brief Set system error
 * 
 * Internal function to record system errors.
 * 
 * @param error_code Error code
 * @param error_msg Error message
 * @return ESP_OK on success
 */
esp_err_t mlx90640_multi_set_error(esp_err_t error_code, const char* error_msg);

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * @brief Get system state name string
 * 
 * @param state System state
 * @return String representation of system state
 */
const char* mlx90640_multi_get_state_name(multi_system_state_t state);

/**
 * @brief Get system uptime in seconds
 * 
 * @return Uptime in seconds since system start
 */
uint32_t mlx90640_multi_get_uptime_seconds(void);

/**
 * @brief Get memory usage estimate
 * 
 * @return Estimated memory usage in bytes
 */
uint32_t mlx90640_multi_get_memory_usage(void);

#ifdef __cplusplus
}
#endif

#endif // MLX90640_MULTI_MANAGER_H
