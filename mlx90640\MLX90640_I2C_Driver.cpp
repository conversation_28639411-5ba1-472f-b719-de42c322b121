/**
 * @copyright (C) 2017 Melexis N.V.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * ESP32 I2C Driver Implementation for MLX90640
 */
#include <stdio.h>
#include "driver/i2c.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "MLX90640_I2C_Driver.h"

#define I2C_MASTER_SCL_IO           8        /*!< GPIO number used for I2C master clock */
#define I2C_MASTER_SDA_IO           3        /*!< GPIO number used for I2C master data  */
#define I2C_MASTER_NUM              0        /*!< I2C master i2c port number, the number of i2c peripheral interfaces available will depend on the chip */
#define I2C_MASTER_FREQ_HZ          800000   /*!< I2C master clock frequency */
#define I2C_MASTER_TX_BUF_DISABLE   0        /*!< I2C master doesn't need buffer */
#define I2C_MASTER_RX_BUF_DISABLE   0        /*!< I2C master doesn't need buffer */
#define I2C_MASTER_TIMEOUT_MS       1000

static const char *TAG = "MLX90640_I2C";

void MLX90640_I2CInit(void)
{
    i2c_port_t i2c_master_port = (i2c_port_t)I2C_MASTER_NUM;

    i2c_config_t conf;
    conf.mode = I2C_MODE_MASTER;
    conf.sda_io_num = (gpio_num_t)I2C_MASTER_SDA_IO;
    conf.scl_io_num = (gpio_num_t)I2C_MASTER_SCL_IO;
    conf.sda_pullup_en = GPIO_PULLUP_ENABLE;
    conf.scl_pullup_en = GPIO_PULLUP_ENABLE;
    conf.master.clk_speed = I2C_MASTER_FREQ_HZ;
    conf.clk_flags = 0;

    esp_err_t err = i2c_param_config(i2c_master_port, &conf);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "I2C param config failed: %s", esp_err_to_name(err));
        return;
    }

    err = i2c_driver_install(i2c_master_port, conf.mode, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "I2C driver install failed: %s", esp_err_to_name(err));
        return;
    }

    ESP_LOGI(TAG, "I2C initialized successfully");
}

int MLX90640_I2CRead(uint8_t slaveAddr, uint16_t startAddress, uint16_t nMemAddressRead, uint16_t *data)
{
    uint8_t* bp = (uint8_t*) data;
    uint8_t write_buf[2];
    esp_err_t ret;

    // Prepare the register address (16-bit, big-endian)
    write_buf[0] = (startAddress >> 8) & 0xFF;
    write_buf[1] = startAddress & 0xFF;

    // Perform I2C write-read transaction
    ret = i2c_master_write_read_device((i2c_port_t)I2C_MASTER_NUM, slaveAddr, write_buf, sizeof(write_buf),
                                       bp, nMemAddressRead * 2, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C read failed: %s", esp_err_to_name(ret));
        return -1;
    }

    // Swap bytes for each 16-bit word (MLX90640 sends MSB first, but we need LSB first for uint16_t)
    for(int cnt = 0; cnt < nMemAddressRead * 2; cnt += 2) {
        uint8_t tmpbytelsb = bp[cnt + 1];
        bp[cnt + 1] = bp[cnt];
        bp[cnt] = tmpbytelsb;
    }

    return 0;
}

int MLX90640_I2CWrite(uint8_t slaveAddr, uint16_t writeAddress, uint16_t data)
{
    uint8_t write_buf[4];
    esp_err_t ret;
    static uint16_t dataCheck;

    // Prepare the write buffer: [reg_addr_high, reg_addr_low, data_high, data_low]
    write_buf[0] = (writeAddress >> 8) & 0xFF;
    write_buf[1] = writeAddress & 0xFF;
    write_buf[2] = (data >> 8) & 0xFF;
    write_buf[3] = data & 0xFF;

    // Perform I2C write transaction
    ret = i2c_master_write_to_device((i2c_port_t)I2C_MASTER_NUM, slaveAddr, write_buf, sizeof(write_buf),
                                     I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C write failed: %s", esp_err_to_name(ret));
        return -1;
    }

    // Verify the write by reading back the data
    if (MLX90640_I2CRead(slaveAddr, writeAddress, 1, &dataCheck) != 0) {
        ESP_LOGE(TAG, "I2C write verification read failed");
        return -1;
    }

    if (writeAddress!=0x8000 &&dataCheck != data) {
        ESP_LOGE(TAG, "I2C write verification failed: expected 0x%04X, got 0x%04X", data, dataCheck);
        return -2;
    }

    return 0;
}

void MLX90640_I2CFreqSet(int freq)
{
    // Note: This function would require reconfiguring the I2C driver
    // For simplicity, we'll just log the request
    ESP_LOGI(TAG, "I2C frequency change requested: %d Hz (not implemented)", freq);
}

