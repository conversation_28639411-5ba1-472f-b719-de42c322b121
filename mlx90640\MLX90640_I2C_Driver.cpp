/**
 * @copyright (C) 2017 Melexis N.V.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * ESP32 I2C Driver Implementation for MLX90640
 */
#include <stdio.h>
#include "driver/i2c.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "MLX90640_I2C_Driver.h"
#include "../mlx90640_multi/mlx90640_i2c_multi.h"

#define I2C_MASTER_SCL_IO           8        /*!< GPIO number used for I2C master clock */
#define I2C_MASTER_SDA_IO           3        /*!< GPIO number used for I2C master data  */
#define I2C_MASTER_NUM              0        /*!< I2C master i2c port number, the number of i2c peripheral interfaces available will depend on the chip */
#define I2C_MASTER_FREQ_HZ          800000   /*!< I2C master clock frequency */
#define I2C_MASTER_TX_BUF_DISABLE   0        /*!< I2C master doesn't need buffer */
#define I2C_MASTER_RX_BUF_DISABLE   0        /*!< I2C master doesn't need buffer */
#define I2C_MASTER_TIMEOUT_MS       1000

static const char *TAG = "MLX90640_I2C";

// ============================================================================
// I2C Context Management for Unified Interface Integration
// ============================================================================

typedef struct {
    i2c_bus_type_t current_bus;        // Current I2C bus type
    bool unified_interface_enabled;    // Whether unified interface is enabled
    SemaphoreHandle_t mutex;           // Mutex for thread safety
    bool initialized;                  // Context initialization status
} mlx90640_i2c_context_t;

// Global I2C context (protected by mutex)
static mlx90640_i2c_context_t g_i2c_context = {
    .current_bus = I2C_BUS_HARDWARE_0,  // Default to hardware bus 0
    .unified_interface_enabled = false,
    .mutex = NULL,
    .initialized = false
};

bool MLX90640_SetI2CContext(i2c_bus_type_t bus_type)
{
    // Initialize context if not already done
    if (!g_i2c_context.initialized) {
        g_i2c_context.mutex = xSemaphoreCreateMutex();
        if (g_i2c_context.mutex == NULL) {
            ESP_LOGE(TAG, "Failed to create I2C context mutex");
            return false;
        }
        g_i2c_context.initialized = true;
    }

    // Validate bus type
    if (bus_type >= I2C_BUS_COUNT) {
        ESP_LOGE(TAG, "Invalid I2C bus type: %d", bus_type);
        return false;
    }

    // Set the context with mutex protection
    if (xSemaphoreTake(g_i2c_context.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        g_i2c_context.current_bus = bus_type;
        g_i2c_context.unified_interface_enabled = true;
        xSemaphoreGive(g_i2c_context.mutex);

        ESP_LOGD(TAG, "I2C context set to bus type %d", bus_type);
        return true;
    } else {
        ESP_LOGE(TAG, "Failed to acquire I2C context mutex");
        return false;
    }
}

i2c_bus_type_t MLX90640_GetI2CContext(void)
{
    if (!g_i2c_context.initialized || g_i2c_context.mutex == NULL) {
        return I2C_BUS_HARDWARE_0;  // Default fallback
    }

    i2c_bus_type_t bus_type = I2C_BUS_HARDWARE_0;

    if (xSemaphoreTake(g_i2c_context.mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        bus_type = g_i2c_context.current_bus;
        xSemaphoreGive(g_i2c_context.mutex);
    }

    return bus_type;
}

bool MLX90640_I2CInit_Unified(void)
{
    ESP_LOGI(TAG, "Initializing MLX90640 I2C through unified interface");

    // Initialize the unified I2C interface
    esp_err_t ret = mlx90640_i2c_multi_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize unified I2C interface: %s", esp_err_to_name(ret));
        return false;
    }

    // Initialize context
    if (!g_i2c_context.initialized) {
        g_i2c_context.mutex = xSemaphoreCreateMutex();
        if (g_i2c_context.mutex == NULL) {
            ESP_LOGE(TAG, "Failed to create I2C context mutex");
            return false;
        }
        g_i2c_context.initialized = true;
        g_i2c_context.unified_interface_enabled = true;
    }

    ESP_LOGI(TAG, "MLX90640 unified I2C interface initialized successfully");
    return true;
}

void MLX90640_I2CInit(void)
{
    i2c_port_t i2c_master_port = (i2c_port_t)I2C_MASTER_NUM;

    i2c_config_t conf;
    conf.mode = I2C_MODE_MASTER;
    conf.sda_io_num = (gpio_num_t)I2C_MASTER_SDA_IO;
    conf.scl_io_num = (gpio_num_t)I2C_MASTER_SCL_IO;
    conf.sda_pullup_en = GPIO_PULLUP_ENABLE;
    conf.scl_pullup_en = GPIO_PULLUP_ENABLE;
    conf.master.clk_speed = I2C_MASTER_FREQ_HZ;
    conf.clk_flags = 0;

    esp_err_t err = i2c_param_config(i2c_master_port, &conf);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "I2C param config failed: %s", esp_err_to_name(err));
        return;
    }

    err = i2c_driver_install(i2c_master_port, conf.mode, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "I2C driver install failed: %s", esp_err_to_name(err));
        return;
    }

    ESP_LOGI(TAG, "I2C initialized successfully");
}

int MLX90640_I2CRead(uint8_t slaveAddr, uint16_t startAddress, uint16_t nMemAddressRead, uint16_t *data)
{
    // Check if unified interface is enabled and use it
    if (g_i2c_context.initialized && g_i2c_context.unified_interface_enabled) {
        i2c_bus_type_t bus_type = MLX90640_GetI2CContext();

        ESP_LOGV(TAG, "Using unified I2C interface, bus type: %d, addr: 0x%02X, reg: 0x%04X, len: %d",
                 bus_type, slaveAddr, startAddress, nMemAddressRead);

        // Use the unified I2C interface
        int result = mlx90640_i2c_unified_read(bus_type, slaveAddr, startAddress, nMemAddressRead, data);

        if (result != 0) {
            ESP_LOGE(TAG, "Unified I2C read failed on bus %d, addr 0x%02X", bus_type, slaveAddr);
        }

        return result;
    }

    // Fallback to original hardware I2C implementation
    ESP_LOGV(TAG, "Using original hardware I2C implementation");

    uint8_t* bp = (uint8_t*) data;
    uint8_t write_buf[2];
    esp_err_t ret;

    // Prepare the register address (16-bit, big-endian)
    write_buf[0] = (startAddress >> 8) & 0xFF;
    write_buf[1] = startAddress & 0xFF;

    // Perform I2C write-read transaction
    ret = i2c_master_write_read_device((i2c_port_t)I2C_MASTER_NUM, slaveAddr, write_buf, sizeof(write_buf),
                                       bp, nMemAddressRead * 2, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C read failed: %s", esp_err_to_name(ret));
        return -1;
    }

    // Swap bytes for each 16-bit word (MLX90640 sends MSB first, but we need LSB first for uint16_t)
    for(int cnt = 0; cnt < nMemAddressRead * 2; cnt += 2) {
        uint8_t tmpbytelsb = bp[cnt + 1];
        bp[cnt + 1] = bp[cnt];
        bp[cnt] = tmpbytelsb;
    }

    return 0;
}

int MLX90640_I2CWrite(uint8_t slaveAddr, uint16_t writeAddress, uint16_t data)
{
    // Check if unified interface is enabled and use it
    if (g_i2c_context.initialized && g_i2c_context.unified_interface_enabled) {
        i2c_bus_type_t bus_type = MLX90640_GetI2CContext();

        ESP_LOGV(TAG, "Using unified I2C interface, bus type: %d, addr: 0x%02X, reg: 0x%04X, data: 0x%04X",
                 bus_type, slaveAddr, writeAddress, data);

        // Use the unified I2C interface
        int result = mlx90640_i2c_unified_write(bus_type, slaveAddr, writeAddress, data);

        if (result != 0) {
            ESP_LOGE(TAG, "Unified I2C write failed on bus %d, addr 0x%02X", bus_type, slaveAddr);
            return result;
        }

        // Verify the write by reading back the data (using the same unified interface)
        uint16_t dataCheck;
        if (MLX90640_I2CRead(slaveAddr, writeAddress, 1, &dataCheck) != 0) {
            ESP_LOGE(TAG, "I2C write verification read failed");
            return -1;
        }

        if (writeAddress != 0x8000 && dataCheck != data) {
            ESP_LOGE(TAG, "I2C write verification failed: expected 0x%04X, got 0x%04X", data, dataCheck);
            return -2;
        }

        return 0;
    }

    // Fallback to original hardware I2C implementation
    ESP_LOGV(TAG, "Using original hardware I2C implementation");

    uint8_t write_buf[4];
    esp_err_t ret;
    static uint16_t dataCheck;

    // Prepare the write buffer: [reg_addr_high, reg_addr_low, data_high, data_low]
    write_buf[0] = (writeAddress >> 8) & 0xFF;
    write_buf[1] = writeAddress & 0xFF;
    write_buf[2] = (data >> 8) & 0xFF;
    write_buf[3] = data & 0xFF;

    // Perform I2C write transaction
    ret = i2c_master_write_to_device((i2c_port_t)I2C_MASTER_NUM, slaveAddr, write_buf, sizeof(write_buf),
                                     I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C write failed: %s", esp_err_to_name(ret));
        return -1;
    }

    // Verify the write by reading back the data
    if (MLX90640_I2CRead(slaveAddr, writeAddress, 1, &dataCheck) != 0) {
        ESP_LOGE(TAG, "I2C write verification read failed");
        return -1;
    }

    if (writeAddress!=0x8000 &&dataCheck != data) {
        ESP_LOGE(TAG, "I2C write verification failed: expected 0x%04X, got 0x%04X", data, dataCheck);
        return -2;
    }

    return 0;
}

void MLX90640_I2CFreqSet(int freq)
{
    // Note: This function would require reconfiguring the I2C driver
    // For simplicity, we'll just log the request
    ESP_LOGI(TAG, "I2C frequency change requested: %d Hz (not implemented)", freq);
}

