/**
 * @file mlx90640_software_i2c.c
 * @brief Software I2C driver implementation for MLX90640 sensors
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include "mlx90640_software_i2c.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "SW_I2C";

// Global software I2C configurations
static sw_i2c_config_t sw_i2c_configs[2] = {0}; // For buses 2 and 3

// ============================================================================
// Utility Functions
// ============================================================================

static inline void sw_i2c_delay_us(uint32_t us)
{
    if (us > 0) {
        esp_rom_delay_us(us);
    }
}

static sw_i2c_config_t* get_sw_i2c_config(i2c_bus_type_t bus_id)
{
    if (bus_id == I2C_BUS_SOFTWARE_2) {
        return &sw_i2c_configs[0];
    } else if (bus_id == I2C_BUS_SOFTWARE_3) {
        return &sw_i2c_configs[1];
    }
    return NULL;
}

// ============================================================================
// GPIO Control Functions
// ============================================================================

static inline void set_sda_high(const sw_i2c_config_t* config)
{
    gpio_set_direction(config->sda_gpio, GPIO_MODE_INPUT);
    gpio_set_pull_mode(config->sda_gpio, GPIO_PULLUP_ONLY);
}

static inline void set_sda_low(const sw_i2c_config_t* config)
{
    gpio_set_direction(config->sda_gpio, GPIO_MODE_OUTPUT);
    gpio_set_level(config->sda_gpio, 0);
}

static inline void set_scl_high(const sw_i2c_config_t* config)
{
    gpio_set_direction(config->scl_gpio, GPIO_MODE_INPUT);
    gpio_set_pull_mode(config->scl_gpio, GPIO_PULLUP_ONLY);
}

static inline void set_scl_low(const sw_i2c_config_t* config)
{
    gpio_set_direction(config->scl_gpio, GPIO_MODE_OUTPUT);
    gpio_set_level(config->scl_gpio, 0);
}

static inline bool read_sda(const sw_i2c_config_t* config)
{
    return gpio_get_level(config->sda_gpio);
}

static inline bool read_scl(const sw_i2c_config_t* config)
{
    return gpio_get_level(config->scl_gpio);
}

// ============================================================================
// Low-level I2C Functions
// ============================================================================

esp_err_t sw_i2c_start(const sw_i2c_config_t* config)
{
    if (!config || !config->initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    // Ensure both lines are high initially
    set_sda_high(config);
    set_scl_high(config);
    sw_i2c_delay_us(SW_I2C_SETUP_TIME_US);

    // Generate start condition: SDA goes low while SCL is high
    set_sda_low(config);
    sw_i2c_delay_us(SW_I2C_HOLD_TIME_US);
    set_scl_low(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    return ESP_OK;
}

esp_err_t sw_i2c_stop(const sw_i2c_config_t* config)
{
    if (!config || !config->initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    // Ensure SDA is low and SCL is low
    set_sda_low(config);
    set_scl_low(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    // Generate stop condition: SCL goes high, then SDA goes high
    set_scl_high(config);
    sw_i2c_delay_us(SW_I2C_SETUP_TIME_US);
    set_sda_high(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    return ESP_OK;
}

esp_err_t sw_i2c_write_byte(const sw_i2c_config_t* config, uint8_t byte)
{
    if (!config || !config->initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    // Write 8 bits, MSB first
    for (int i = 7; i >= 0; i--) {
        if (byte & (1 << i)) {
            set_sda_high(config);
        } else {
            set_sda_low(config);
        }
        sw_i2c_delay_us(SW_I2C_SETUP_TIME_US);

        // Clock pulse
        set_scl_high(config);
        sw_i2c_delay_us(SW_I2C_DELAY_US);
        set_scl_low(config);
        sw_i2c_delay_us(SW_I2C_HOLD_TIME_US);
    }

    // Read ACK bit
    set_sda_high(config);  // Release SDA for slave to control
    sw_i2c_delay_us(SW_I2C_SETUP_TIME_US);
    set_scl_high(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    bool ack = !read_sda(config);  // ACK is low
    set_scl_low(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    return ack ? ESP_OK : ESP_ERR_TIMEOUT;
}

esp_err_t sw_i2c_read_byte(const sw_i2c_config_t* config, uint8_t* byte, bool ack)
{
    if (!config || !config->initialized || !byte) {
        return ESP_ERR_INVALID_ARG;
    }

    *byte = 0;
    set_sda_high(config);  // Release SDA for slave to control

    // Read 8 bits, MSB first
    for (int i = 7; i >= 0; i--) {
        sw_i2c_delay_us(SW_I2C_SETUP_TIME_US);
        set_scl_high(config);
        sw_i2c_delay_us(SW_I2C_DELAY_US);

        if (read_sda(config)) {
            *byte |= (1 << i);
        }

        set_scl_low(config);
        sw_i2c_delay_us(SW_I2C_HOLD_TIME_US);
    }

    // Send ACK/NACK
    if (ack) {
        set_sda_low(config);   // ACK
    } else {
        set_sda_high(config);  // NACK
    }
    sw_i2c_delay_us(SW_I2C_SETUP_TIME_US);

    set_scl_high(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);
    set_scl_low(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    set_sda_high(config);  // Release SDA

    return ESP_OK;
}

// ============================================================================
// High-level I2C Functions
// ============================================================================

esp_err_t sw_i2c_init(i2c_bus_type_t bus_id)
{
    if (bus_id != I2C_BUS_SOFTWARE_2 && bus_id != I2C_BUS_SOFTWARE_3) {
        ESP_LOGE(TAG, "Invalid software I2C bus ID: %d", bus_id);
        return ESP_ERR_INVALID_ARG;
    }

    sw_i2c_config_t* config = get_sw_i2c_config(bus_id);
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    // Configure GPIO pins based on bus ID
    if (bus_id == I2C_BUS_SOFTWARE_2) {
        config->sda_gpio = I2C_SW2_SDA_GPIO;
        config->scl_gpio = I2C_SW2_SCL_GPIO;
    } else {
        config->sda_gpio = I2C_SW3_SDA_GPIO;
        config->scl_gpio = I2C_SW3_SCL_GPIO;
    }

    config->frequency = I2C_SOFTWARE_FREQ_HZ;

    // Initialize GPIO pins
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT_OD,
        .pin_bit_mask = (1ULL << config->sda_gpio) | (1ULL << config->scl_gpio),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_ENABLE,
    };

    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure GPIO for software I2C bus %d: %s", 
                 bus_id, esp_err_to_name(ret));
        return ret;
    }

    // Set initial state (both lines high)
    set_sda_high(config);
    set_scl_high(config);
    sw_i2c_delay_us(SW_I2C_DELAY_US);

    config->initialized = true;

    ESP_LOGI(TAG, "Software I2C bus %d initialized (SDA:%d, SCL:%d)",
             bus_id, config->sda_gpio, config->scl_gpio);

    return ESP_OK;
}

esp_err_t sw_i2c_deinit(i2c_bus_type_t bus_id)
{
    sw_i2c_config_t* config = get_sw_i2c_config(bus_id);
    if (!config || !config->initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    // Reset GPIO pins
    gpio_reset_pin(config->sda_gpio);
    gpio_reset_pin(config->scl_gpio);

    config->initialized = false;
    ESP_LOGI(TAG, "Software I2C bus %d deinitialized", bus_id);

    return ESP_OK;
}

bool sw_i2c_device_present(i2c_bus_type_t bus_id, uint8_t device_addr)
{
    sw_i2c_config_t* config = get_sw_i2c_config(bus_id);
    if (!config || !config->initialized) {
        return false;
    }

    esp_err_t ret;

    // Start condition
    ret = sw_i2c_start(config);
    if (ret != ESP_OK) {
        return false;
    }

    // Send device address with write bit
    ret = sw_i2c_write_byte(config, (device_addr << 1) | 0);

    // Stop condition
    sw_i2c_stop(config);

    return (ret == ESP_OK);
}

esp_err_t sw_i2c_write(i2c_bus_type_t bus_id, uint8_t device_addr,
                       uint16_t reg_addr, const uint8_t* data, size_t len)
{
    if (!data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    sw_i2c_config_t* config = get_sw_i2c_config(bus_id);
    if (!config || !config->initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret;

    // Start condition
    ret = sw_i2c_start(config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to generate start condition");
        return ret;
    }

    // Send device address with write bit
    ret = sw_i2c_write_byte(config, (device_addr << 1) | 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Device address NACK");
        sw_i2c_stop(config);
        return ret;
    }

    // Send register address (16-bit, MSB first for MLX90640)
    ret = sw_i2c_write_byte(config, (reg_addr >> 8) & 0xFF);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Register address MSB NACK");
        sw_i2c_stop(config);
        return ret;
    }

    ret = sw_i2c_write_byte(config, reg_addr & 0xFF);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Register address LSB NACK");
        sw_i2c_stop(config);
        return ret;
    }

    // Send data bytes
    for (size_t i = 0; i < len; i++) {
        ret = sw_i2c_write_byte(config, data[i]);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Data byte %zu NACK", i);
            sw_i2c_stop(config);
            return ret;
        }
    }

    // Stop condition
    ret = sw_i2c_stop(config);
    return ret;
}

esp_err_t sw_i2c_read(i2c_bus_type_t bus_id, uint8_t device_addr,
                      uint16_t reg_addr, uint8_t* data, size_t len)
{
    if (!data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    sw_i2c_config_t* config = get_sw_i2c_config(bus_id);
    if (!config || !config->initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret;

    // Write phase: Send register address
    ret = sw_i2c_start(config);
    if (ret != ESP_OK) {
        return ret;
    }

    ret = sw_i2c_write_byte(config, (device_addr << 1) | 0);
    if (ret != ESP_OK) {
        sw_i2c_stop(config);
        return ret;
    }

    ret = sw_i2c_write_byte(config, (reg_addr >> 8) & 0xFF);
    if (ret != ESP_OK) {
        sw_i2c_stop(config);
        return ret;
    }

    ret = sw_i2c_write_byte(config, reg_addr & 0xFF);
    if (ret != ESP_OK) {
        sw_i2c_stop(config);
        return ret;
    }

    // Repeated start for read phase
    ret = sw_i2c_start(config);
    if (ret != ESP_OK) {
        return ret;
    }

    ret = sw_i2c_write_byte(config, (device_addr << 1) | 1);
    if (ret != ESP_OK) {
        sw_i2c_stop(config);
        return ret;
    }

    // Read data bytes
    for (size_t i = 0; i < len; i++) {
        bool ack = (i < len - 1);  // ACK all bytes except the last one
        ret = sw_i2c_read_byte(config, &data[i], ack);
        if (ret != ESP_OK) {
            sw_i2c_stop(config);
            return ret;
        }
    }

    ret = sw_i2c_stop(config);
    return ret;
}
