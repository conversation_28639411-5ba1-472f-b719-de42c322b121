# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-01 15:12:55 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   

## Key Features

* **Thermal Anomaly Detection System**: Advanced chess-pattern based thermal anomaly detection using MLX90640 sensor with optimized algorithms for ESP32 platform
* **Real-time Processing**: Processes 24x32 thermal frames at ~10 FPS with sliding window history analysis using circular buffer optimization
* **Dual-region Detection**: Detects anomalies in even/odd chess patterns and triggers alerts when centers are within threshold distance
* **ESP32 Optimized**: Memory-efficient implementation with personalized thresholds, inline calculations, running sum matrices, and O(n) complexity algorithms
* **1D Array Processing**: Direct support for MLX90640 native 1D data format (768 float values) without conversion overhead
* **UART Data Transmission**: Structured data transmission of detection results with frame synchronization and checksums
* **Adaptive Learning System**: Complete state machine implementation (WARMUP → LEARNING → JUDGING) with online threshold generation from 1000 valid frames, automatic noise adaptation, and reconstruction capability for changing environments
* **Multi-Sensor MLX90640 System**: Complete 4-sensor thermal detection system with quad I2C buses (2 hardware + 2 software), one-to-one sensor mapping, dual-core FreeRTOS architecture, and ESP32-S3 SPIRAM optimization for industrial applications

## Overall Architecture

*

[2025-07-03 14:03:30] - New feature: Completed ESP32 thermal anomaly detection optimization and integration project with all requested optimizations implemented
[2025-07-03 14:16:48] - New feature: Completed all remaining optimizations: circular buffer with running sum and 1D array direct processing support
[2025-07-11 15:47:04] - New feature: Added complete learning stage implementation to thermal anomaly detection system with state machine support
[2025-07-22 14:53:24] - New feature: 完成4个MLX90640传感器扩展方案，包括ESP32-S3内存优化和SPIRAM配置