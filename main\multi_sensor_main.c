/**
 * @file multi_sensor_main.c
 * @brief Main application for 4-sensor MLX90640 thermal detection system
 * 
 * This application demonstrates the complete 4-sensor MLX90640 system with:
 * - 4 independent I2C buses (2 hardware + 2 software)
 * - One-to-one sensor mapping (no GPIO switches needed)
 * - Dual-core processing (Core 0: acquisition, Core 1: processing)
 * - Thermal anomaly detection with learning capabilities
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"
// #include "nvs_flash.h"

// Multi-sensor system includes
#include "../mlx90640_multi/mlx90640_multi_manager.h"
#include "../mlx90640_multi/multi_sensor_init.h"
#include "../mlx90640_multi/multi_sensor_tasks.h"
#include "../thermalDetection/thermal_anomaly_core.h"

static const char *TAG = "MULTI_SENSOR_MAIN";

// ============================================================================
// Application Configuration
// ============================================================================

#define SYSTEM_MONITOR_INTERVAL_MS  5000    // System status logging interval
#define PERFORMANCE_LOG_INTERVAL_MS 10000   // Performance logging interval
#define WATCHDOG_CHECK_INTERVAL_MS  1000    // Task health check interval

// ============================================================================
// System Monitoring Task
// ============================================================================

/**
 * @brief System monitoring task
 * 
 * This task runs on Core 1 and provides:
 * - System status monitoring
 * - Performance metrics logging
 * - Task health checking
 * - Memory usage monitoring
 */
void system_monitor_task(void* param)
{
    uint32_t last_status_log = 0;
    uint32_t last_performance_log = 0;
    uint32_t current_time;
    
    ESP_LOGI(TAG, "System monitor task started (Core %d)", xPortGetCoreID());
    
    while (1) {
        current_time = esp_timer_get_time() / 1000; // Convert to milliseconds
        
        // Check system health
        if (!mlx90640_multi_system_is_healthy()) {
            ESP_LOGW(TAG, "System health check failed!");
            // TODO: Add recovery actions here
        }
        
        // Log system status periodically
        if (current_time - last_status_log >= SYSTEM_MONITOR_INTERVAL_MS) {
            mlx90640_multi_log_system_status();
            last_status_log = current_time;
        }
        
        // Log performance metrics periodically
        if (current_time - last_performance_log >= PERFORMANCE_LOG_INTERVAL_MS) {
            multi_sensor_log_performance_summary();
            multi_sensor_log_task_status();
            last_performance_log = current_time;
        }
        
        // Check task health
        if (!multi_sensor_all_tasks_healthy()) {
            ESP_LOGW(TAG, "Task health check failed!");
            // TODO: Add task recovery actions here
        }
        
        // Sleep until next check
        vTaskDelay(pdMS_TO_TICKS(WATCHDOG_CHECK_INTERVAL_MS));
    }
}

// ============================================================================
// Application Initialization
// ============================================================================

// /**
//  * @brief Initialize NVS (Non-Volatile Storage)
//  */
// esp_err_t init_nvs(void)
// {
//     esp_err_t ret = nvs_flash_init();
//     if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
//         ESP_LOGW(TAG, "NVS partition was truncated and needs to be erased");
//         ESP_ERROR_CHECK(nvs_flash_erase());
//         ret = nvs_flash_init();
//     }
//     return ret;
// }

/**
 * @brief Initialize thermal detection configuration
 */
thermal_config_t create_thermal_config(void)
{
    thermal_config_t config = {
        .pre_filter_min = 0.0f,
        .pre_filter_max = 80.0f,
        .pre_filter_delta = 10.0f,
        .window_size = 20,
        .center_distance_threshold = 5.0f,
        .center_distance_threshold_squared = 25.0f,
        .min_anomaly_pixels = 5,
        .enable_pre_filter = true,
        .enable_learning = true,
        .warmup_time_ms = 5000
    };
    
    ESP_LOGI(TAG, "Thermal detection configuration:");
    ESP_LOGI(TAG, "  Learning enabled: %s", config.enable_learning ? "Yes" : "No");
    ESP_LOGI(TAG, "  Window size: %d frames", config.window_size);
    ESP_LOGI(TAG, "  Warmup time: %u ms", (unsigned)config.warmup_time_ms);
    ESP_LOGI(TAG, "  Pre-filter range: %.1f - %.1f °C", config.pre_filter_min, config.pre_filter_max);
    ESP_LOGI(TAG, "  Center distance threshold: %.2f pixels", config.center_distance_threshold);
    ESP_LOGI(TAG, "  Learning enabled: %s", config.enable_learning ? "Yes" : "No");
    
    return config;
}

// ============================================================================
// Main Application Function
// ============================================================================

void app_main(void)
{
    esp_err_t ret;
    
    ESP_LOGI(TAG, "=== 4-Sensor MLX90640 Thermal Detection System ===");
    ESP_LOGI(TAG, "Starting multi-sensor thermal detection application");
    ESP_LOGI(TAG, "ESP32 Chip: %s", esp_get_idf_version());
    ESP_LOGI(TAG, "Free heap: %u bytes", (unsigned)esp_get_free_heap_size());
    
    // Initialize NVS
    // ESP_LOGI(TAG, "Initializing NVS...");
    // ret = init_nvs();
    // if (ret != ESP_OK) {
    //     ESP_LOGE(TAG, "Failed to initialize NVS: %s", esp_err_to_name(ret));
    //     return;
    // }
    
    // Create thermal detection configuration
    thermal_config_t thermal_config = create_thermal_config();
    
    // Initialize multi-sensor system
    ESP_LOGI(TAG, "Initializing multi-sensor system...");
    ret = mlx90640_multi_system_init(&thermal_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize multi-sensor system: %s", esp_err_to_name(ret));
        
        // Try to get error details
        esp_err_t error_code;
        char error_msg[128];
        if (mlx90640_multi_get_last_error(&error_code, error_msg, sizeof(error_msg)) == ESP_OK) {
            ESP_LOGE(TAG, "System error details: %s - %s", esp_err_to_name(error_code), error_msg);
        }
        return;
    }
    
    ESP_LOGI(TAG, "Multi-sensor system initialized successfully");
    ESP_LOGI(TAG, "System state: %s", 
             mlx90640_multi_get_state_name(mlx90640_multi_get_system_state()));
    
    // Start the multi-sensor system
    ESP_LOGI(TAG, "Starting multi-sensor system...");
    ret = mlx90640_multi_system_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start multi-sensor system: %s", esp_err_to_name(ret));
        mlx90640_multi_system_shutdown();
        return;
    }
    
    ESP_LOGI(TAG, "Multi-sensor system started successfully");
    ESP_LOGI(TAG, "All 4 sensors are now running on independent I2C buses:");
    ESP_LOGI(TAG, "  - Sensor 0: Hardware I2C Bus 0 (GPIO 21,22), Address 0x33");
    ESP_LOGI(TAG, "  - Sensor 1: Hardware I2C Bus 1 (GPIO 18,19), Address 0x33");
    ESP_LOGI(TAG, "  - Sensor 2: Software I2C Bus 2 (GPIO 4,5), Address 0x33");
    ESP_LOGI(TAG, "  - Sensor 3: Software I2C Bus 3 (GPIO 16,17), Address 0x33");
    
    // Create system monitoring task
    ESP_LOGI(TAG, "Creating system monitor task...");
    TaskHandle_t monitor_task_handle;
    BaseType_t task_ret = xTaskCreatePinnedToCore(
        system_monitor_task,        // Task function
        "system_monitor",           // Task name
        4096,                       // Stack size
        NULL,                       // Task parameter
        2,                          // Priority
        &monitor_task_handle,       // Task handle
        1                           // Core ID (Core 1)
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create system monitor task");
        mlx90640_multi_system_stop();
        mlx90640_multi_system_shutdown();
        return;
    }
    
    ESP_LOGI(TAG, "System monitor task created successfully");
    
    // Log initial system status
    mlx90640_multi_log_system_status();
    
    ESP_LOGI(TAG, "=== System Startup Complete ===");
    ESP_LOGI(TAG, "The system is now running. Monitor logs for sensor data and anomaly detection.");
    ESP_LOGI(TAG, "System will log status every %d seconds", SYSTEM_MONITOR_INTERVAL_MS / 1000);
    ESP_LOGI(TAG, "Performance metrics will be logged every %d seconds", PERFORMANCE_LOG_INTERVAL_MS / 1000);
    
    // Main loop - just keep the system running
    while (1) {
        // Check if system is still healthy
        if (mlx90640_multi_get_system_state() == MULTI_SYSTEM_ERROR) {
            ESP_LOGE(TAG, "System entered error state - attempting recovery");
            
            // Try to restart the system
            mlx90640_multi_system_stop();
            vTaskDelay(pdMS_TO_TICKS(1000));
            
            ret = mlx90640_multi_system_start();
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "System recovery failed - shutting down");
                break;
            }
            
            ESP_LOGI(TAG, "System recovery successful");
        }
        
        // Sleep for a while
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
    
    // Cleanup on exit
    ESP_LOGI(TAG, "Shutting down multi-sensor system...");
    mlx90640_multi_system_stop();
    mlx90640_multi_system_shutdown();
    
    ESP_LOGI(TAG, "Application terminated");
}
