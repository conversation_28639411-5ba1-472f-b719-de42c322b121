# Multi-sensor MLX90640 thermal detection system
# Supports both single sensor (blink_example_main.c) and 4-sensor (multi_sensor_main.c) modes

# Choose main application:
# - For single sensor: use "blink_example_main.c"
# - For 4-sensor system: use "multi_sensor_main.c"
set(MAIN_APP "multi_sensor_main.c")

idf_component_register(SRCS "${MAIN_APP}"
                            # Original MLX90640 driver
                            "../mlx90640/MLX90640_I2C_Driver.cpp"
                            "../mlx90640/MLX90640_API.cpp"
                            "../mlx90640/mlx90640_lcd_display.c"
                            # Thermal detection core
                            "../thermalDetection/thermal_anomaly_core.c"
                            # Multi-sensor system files
                            "../mlx90640_multi/mlx90640_i2c_multi.c"
                            "../mlx90640_multi/mlx90640_instance.c"
                            "../mlx90640_multi/mlx90640_multi_manager.c"
                            "../mlx90640_multi/multi_sensor_init.c"
                            "../mlx90640_multi/multi_sensor_tasks.c"
                            "../mlx90640_multi/mlx90640_software_i2c.c"
                       INCLUDE_DIRS "."
                                   "../mlx90640"
                                   "../thermalDetection"
                                   "../mlx90640_multi"
                       REQUIRES driver esp_timer nvs_flash)
