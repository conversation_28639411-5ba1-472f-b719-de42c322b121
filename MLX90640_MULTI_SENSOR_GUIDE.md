# MLX90640 4传感器系统使用指导

## 🎯 系统概述

本项目实现了一个完整的4个MLX90640热成像传感器系统，专为ESP32-S3设计，具有以下特性：

### 核心特性
- **4传感器支持**: 同时管理4个MLX90640传感器
- **双I2C总线**: 避免地址冲突，提高通信可靠性
- **双核架构**: Core 0负责数据获取，Core 1负责数据处理
- **SPIRAM优化**: 充分利用ESP32-S3的SPIRAM资源
- **热检测集成**: 内置学习和判断阶段的热异常检测算法

## 🔧 硬件连接

### 传感器配置
```
传感器标识    I2C总线    设备地址    GPIO控制
Sensor Ax    I2C_0      0x34       无 (常供电)
Sensor Ay    I2C_0      0x33       GPIO 25 (模拟开关Sa)
Sensor Bx    I2C_1      0x34       无 (常供电)
Sensor By    I2C_1      0x33       GPIO 26 (模拟开关Sb)
```

### GPIO引脚分配
```
功能              GPIO引脚    说明
I2C_0 SDA        GPIO 21     I2C总线A数据线
I2C_0 SCL        GPIO 22     I2C总线A时钟线
I2C_1 SDA        GPIO 18     I2C总线B数据线
I2C_1 SCL        GPIO 19     I2C总线B时钟线
Switch Sa        GPIO 25     控制Ay传感器供电
Switch Sb        GPIO 26     控制By传感器供电
```

### 连接示意图
```
ESP32-S3
├── I2C Bus A (GPIO 21,22)
│   ├── Sensor Ax (0x34) ← 常供电
│   └── Sensor Ay (0x33) ← GPIO 25控制
└── I2C Bus B (GPIO 18,19)
    ├── Sensor Bx (0x34) ← 常供电
    └── Sensor By (0x33) ← GPIO 26控制
```

## 🚀 编译和烧录

### 1. 环境准备
确保已安装ESP-IDF 5.4.2或更高版本。

### 2. 配置项目
```bash
cd /path/to/project
idf.py menuconfig
```

关键配置项：
- **ESP PSRAM**: 启用SPIRAM支持
- **Component config → ESP32S3-Specific**: 启用SPIRAM支持
- **Component config → FreeRTOS**: 启用双核支持

### 3. 编译项目
```bash
idf.py build
```

### 4. 烧录到设备
```bash
idf.py flash monitor
```

## 📊 内存使用情况

编译成功后的内存使用：
```
Memory Type        Used        Used%    Remain      Total
DIRAM             254,683     74.52%    87,077     341,760
Flash Code        157,770       -         -           -
Flash Data         62,692       -         -           -
IRAM               16,383     99.99%        1      16,384
```

**SPIRAM分配**:
- 温度数据缓冲区: 4 × 3KB = 12KB
- 热检测器实例: 4 × 70KB = 280KB
- 总SPIRAM使用: ~292KB

## 🔄 系统工作流程

### 初始化序列
1. **GPIO初始化**: 配置模拟开关控制引脚
2. **I2C总线初始化**: 设置双I2C总线
3. **传感器地址修改**: Ax和Bx改为0x34地址
4. **内存分配**: 在SPIRAM中分配大数据缓冲区
5. **任务创建**: 创建8个FreeRTOS任务（4个获取+4个处理）

### 运行时架构
```
Core 0 (数据获取)          Core 1 (数据处理)
┌─────────────────┐       ┌─────────────────┐
│ acquire_task_Ax │──────→│ process_task_Ax │
│ acquire_task_Ay │──────→│ process_task_Ay │
│ acquire_task_Bx │──────→│ process_task_Bx │
│ acquire_task_By │──────→│ process_task_By │
└─────────────────┘       └─────────────────┘
        │                         │
        ↓                         ↓
   FreeRTOS队列              热检测处理
```

## 🛠️ API使用示例

### 基本使用
```c
#include "mlx90640_multi_manager.h"

void app_main(void)
{
    // 1. 初始化系统
    esp_err_t ret = mlx90640_multi_system_init(NULL);
    if (ret != ESP_OK) {
        ESP_LOGE("MAIN", "System init failed: %s", esp_err_to_name(ret));
        return;
    }
    
    // 2. 启动系统
    ret = mlx90640_multi_system_start();
    if (ret != ESP_OK) {
        ESP_LOGE("MAIN", "System start failed: %s", esp_err_to_name(ret));
        return;
    }
    
    // 3. 系统监控循环
    while (1) {
        // 检查系统健康状态
        if (!mlx90640_multi_system_is_healthy()) {
            ESP_LOGW("MAIN", "System health check failed");
        }
        
        // 记录系统状态
        mlx90640_multi_log_system_status();
        
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
}
```

### 获取传感器数据
```c
// 获取特定传感器实例
mlx90640_instance_t* sensor = mlx90640_multi_get_sensor(SENSOR_AX);
if (sensor != NULL) {
    float temp_data[THERMAL_PIXELS];
    esp_err_t ret = mlx90640_instance_get_temp_data(sensor, temp_data, 1000);
    if (ret == ESP_OK) {
        // 处理温度数据
        ESP_LOGI("MAIN", "Got temperature data from sensor Ax");
    }
}
```

## 📈 性能优化建议

### 1. 任务优先级调整
根据应用需求调整任务优先级：
```c
#define ACQUIRE_TASK_PRIORITY   5     // 数据获取优先级
#define PROCESS_TASK_PRIORITY   3     // 数据处理优先级
```

### 2. 帧率控制
调整帧率以平衡性能和功耗：
```c
#define FRAME_INTERVAL_MS       100   // 100ms = 10 FPS
```

### 3. 内存优化
- 大数据缓冲区使用SPIRAM
- 小控制结构保持在DRAM
- 避免频繁的内存分配/释放

## 🐛 故障排除

### 常见问题

**1. 编译时DRAM溢出**
- 确保启用了SPIRAM支持
- 检查大数据结构是否正确分配到SPIRAM

**2. 传感器通信失败**
- 检查I2C连接和上拉电阻
- 验证传感器地址是否正确
- 确认模拟开关控制正常

**3. 任务创建失败**
- 检查可用内存
- 调整任务栈大小
- 验证FreeRTOS配置

### 调试技巧

**启用详细日志**:
```c
#define ENABLE_MULTI_SENSOR_DEBUG   1
```

**监控内存使用**:
```bash
idf.py size
```

**检查任务状态**:
```c
mlx90640_multi_log_system_status();
```

## 📝 配置参数

### 热检测配置
```c
thermal_config_t config = {
    .pre_filter_min = 0.0f,           // 最小有效温度
    .pre_filter_max = 80.0f,          // 最大有效温度
    .pre_filter_delta = 10.0f,        // 最大温度变化
    .window_size = 20,                // 滑动窗口大小
    .center_distance_threshold = 5.0f, // 中心距离阈值
    .min_anomaly_pixels = 5,          // 最小异常像素数
    .enable_pre_filter = true,        // 启用预过滤
    .enable_learning = true,          // 启用学习阶段
    .warmup_time_ms = 5000           // 预热时间
};
```

### GPIO配置修改
如需修改GPIO引脚，编辑 `mlx90640_multi_config.h`:
```c
#define I2C_A_SDA_GPIO          21    // 修改为所需引脚
#define I2C_A_SCL_GPIO          22
#define SWITCH_SA_GPIO          25
// ... 其他引脚配置
```

## 🔮 扩展建议

### 1. 添加更多传感器
- 扩展I2C总线数量
- 增加GPIO控制引脚
- 调整内存分配策略

### 2. 数据输出接口
- UART数据传输
- WiFi网络传输
- SD卡数据存储

### 3. 高级功能
- 温度校准算法
- 多传感器数据融合
- 实时图像显示

---

**项目完成时间**: 2025-07-22  
**ESP-IDF版本**: 5.4.2  
**目标芯片**: ESP32-S3  
**内存优化**: SPIRAM支持  
