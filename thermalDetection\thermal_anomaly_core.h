/**
 * @file thermal_anomaly_core.h
 * @brief Core Thermal Anomaly Detection Algorithm
 * 
 * This header contains only the essential algorithm for processing
 * 32x24 thermal data arrays, extracted from the personalized detector.
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */

#ifndef THERMAL_ANOMALY_CORE_H
#define THERMAL_ANOMALY_CORE_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Thermal sensor dimensions
#define THERMAL_HEIGHT 24
#define THERMAL_WIDTH  32
#ifndef THERMAL_PIXELS
#define THERMAL_PIXELS (THERMAL_HEIGHT * THERMAL_WIDTH)
#endif

// Default configuration values
#define DEFAULT_PRE_FILTER_MIN      0.0f    // °C
#define DEFAULT_PRE_FILTER_MAX      60.0f   // °C
#define DEFAULT_PRE_FILTER_DELTA    40.0f   // °C
#define DEFAULT_WINDOW_SIZE         15      // frames
#define DEFAULT_CENTER_DISTANCE     20.0f   // pixels
#define DEFAULT_MIN_ANOMALY_PIXELS  1       // pixels

// Learning stage constants
#define MAX_COLLECT                 1000    // 学习阶段需采集的有效帧
#define RESTART_LRN                 10      // 学习阶段连续问题帧重启阈值
#define RESTART_JUD                 2000    // 判断阶段连续问题帧重构阈值
#define THR_MULT                    1.0f   // 阈值放大系数
#define TEMP_MIN                    10.0f   // 无效帧判定下限 (°C)
#define TEMP_MAX                    60.0f   // 无效帧判定上限 (°C)
#define DEFAULT_WARMUP_TIME_MS      5000    // 默认预热时间 (ms)

/**
 * @brief System state enumeration
 */
typedef enum {
    STATE_WARMUP,                   ///< 预热阶段，什么都不做
    STATE_LEARNING,                 ///< 学习阶段，收集1000有效帧生成阈值矩阵
    STATE_JUDGING                   ///< 判断阶段，使用生成的阈值进行异常检测
} thermal_detector_state_t;

/**
 * @brief Configuration parameters
 */
typedef struct {
    float pre_filter_min;           ///< Minimum valid temperature (°C)
    float pre_filter_max;           ///< Maximum valid temperature (°C)
    float pre_filter_delta;         ///< Maximum temperature change (°C)
    uint8_t window_size;            ///< Sliding window size (frames)
    float center_distance_threshold; ///< Center distance threshold (pixels)
    float center_distance_threshold_squared; ///< Pre-calculated squared threshold for optimization
    uint8_t min_anomaly_pixels;     ///< Minimum anomaly pixels for detection
    bool enable_pre_filter;         ///< Enable pre-filtering
    bool enable_learning;           ///< Enable learning stage (if false, start directly in judging)
    uint32_t warmup_time_ms;        ///< Warmup time in milliseconds
} thermal_config_t;

/**
 * @brief Anomaly center coordinates
 */
typedef struct {
    float x, y;                     ///< Center coordinates
    bool valid;                     ///< Whether center is valid
} anomaly_center_t;

/**
 * @brief Detection result
 */
typedef struct {
    uint32_t frame_count;           ///< Current frame number
    bool is_filtered;               ///< Whether frame was pre-filtered
    bool anomaly_detected;          ///< Whether anomaly was detected
    bool trigger_anomaly;           ///< Whether trigger condition met
    anomaly_center_t anomaly_center; ///< Current anomaly center
    uint16_t anomaly_pixel_count;   ///< Number of anomaly pixels
    float center_distance;          ///< Distance between even/odd centers
    bool is_even_frame;             ///< Whether this is an even frame

    // State machine info
    thermal_detector_state_t current_state; ///< Current detector state
    int collected_frames;           ///< Number of frames collected in learning stage
    int consecutive_problem_frames; ///< Consecutive problem frames count
    bool learning_completed;        ///< Whether learning stage is completed

    // Debug info
    anomaly_center_t even_center;   ///< Even region center
    anomaly_center_t odd_center;    ///< Odd region center
    uint16_t updated_pixels;        ///< Number of pixels updated this frame
} thermal_result_t;

/**
 * @brief Core detector state
 */
typedef struct {
    thermal_config_t config;

    // State machine
    thermal_detector_state_t state;
    uint32_t warmup_start_time;     ///< Warmup start time (implementation dependent)

    // Thresholds - maximum memory optimization
    float (*pixel_thresholds)[THERMAL_WIDTH];      ///< Mutable thresholds - points to library-declared memory, used for learning and judging
    const float (*pre_threshold)[THERMAL_WIDTH];   ///< Pre-thresholds (for learning stage) - points to external pre_pixel_thresholds

    // Sliding window history data
    float history_buffer[DEFAULT_WINDOW_SIZE][THERMAL_HEIGHT][THERMAL_WIDTH];
    float pixel_history_mean[THERMAL_HEIGHT][THERMAL_WIDTH];
    float pixel_history_sum[THERMAL_HEIGHT][THERMAL_WIDTH];  ///< Running sum for efficient mean calculation
    uint8_t buffer_index;           ///< Current write position in circular buffer
    uint8_t frames_stored;          ///< Number of frames stored so far

    // Learning stage variables
    int collected_frames;           ///< Number of valid frames collected in learning stage
    int consecutive_problem_frames; ///< Consecutive problem frames count
    bool learning_completed;        ///< Whether learning stage is completed

    // State variables
    uint32_t frame_count;
    bool is_initialized;
    anomaly_center_t last_anomaly_centers[2]; ///< [0]=even, [1]=odd
    float last_valid_frame[THERMAL_HEIGHT][THERMAL_WIDTH]; ///< For pre-filtering
    bool has_last_valid;            ///< Whether last_valid_frame is valid
} thermal_detector_t;

/**
 * @brief Initialize thermal detector
 *
 * @param detector Pointer to detector structure
 * @param config Configuration parameters (NULL for defaults)
 * @param thresholds Personalized threshold array (NULL for default uniform thresholds)
 * @param pre_thresholds Pre-threshold array for learning stage (NULL for default)
 * @return 0 on success, negative error code otherwise
 */
int thermal_detector_init(thermal_detector_t* detector,
                         const thermal_config_t* config,
                         const float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH]);

/**
 * @brief Process a thermal frame (main algorithm function)
 * 
 * @param detector Pointer to detector structure
 * @param thermal_frame Input thermal frame [24][32] in °C
 * @param result Output detection result
 * @return 0 on success, negative error code otherwise
 */
int thermal_detector_process_frame(thermal_detector_t* detector,
                                  const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                  thermal_result_t* result);

/// Process thermal frame from 1D array (MLX90640 format: 768 values in row-major order)
int thermal_detector_process_frame_1d(thermal_detector_t* detector,
                                     const float* thermal_frame_1d,
                                     thermal_result_t* result);

/**
 * @brief Process frame with full state machine support (warmup -> learning -> judging)
 *
 * @param detector Pointer to detector structure
 * @param thermal_frame Input thermal frame [24][32] in °C
 * @param result Output detection result
 * @return 0 on success, negative error code otherwise
 */
int thermal_detector_process_frame_with_learning(thermal_detector_t* detector,
                                               const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                               thermal_result_t* result);

/**
 * @brief Process frame with full state machine support from 1D array
 *
 * @param detector Pointer to detector structure
 * @param thermal_frame_1d Input thermal frame as 1D array (768 values)
 * @param result Output detection result
 * @return 0 on success, negative error code otherwise
 */
int thermal_detector_process_frame_1d_with_learning(thermal_detector_t* detector,
                                                  const float* thermal_frame_1d,
                                                  thermal_result_t* result);

/**
 * @brief Reset detector state
 * 
 * @param detector Pointer to detector structure
 * @return 0 on success, negative error code otherwise
 */
int thermal_detector_reset(thermal_detector_t* detector);

/**
 * @brief Create default configuration
 * 
 * @param config Output configuration structure
 */
void thermal_detector_create_default_config(thermal_config_t* config);

/**
 * @brief Create default uniform thresholds
 *
 * @param thresholds Output threshold array
 * @param threshold_value Uniform threshold value (default: 0.07)
 */
void thermal_detector_create_default_thresholds(float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH],
                                               float threshold_value);

/**
 * @brief Get current learning progress (0.0 to 1.0)
 *
 * @param detector Pointer to detector structure
 * @return Learning progress (0.0 = not started, 1.0 = completed)
 */
float thermal_detector_get_learning_progress(const thermal_detector_t* detector);

/**
 * @brief Force transition to judging stage (skip learning)
 *
 * @param detector Pointer to detector structure
 * @return 0 on success, negative error code otherwise
 */
int thermal_detector_skip_learning(thermal_detector_t* detector);

#ifdef __cplusplus
}
#endif

#endif // THERMAL_ANOMALY_CORE_H
