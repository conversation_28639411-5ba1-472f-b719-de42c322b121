/**
 * @file thermal_learning_example.c
 * @brief Example demonstrating thermal anomaly detection with learning stage
 *
 * This example shows how to use the complete state machine:
 * STATE_WARMUP -> STATE_LEARNING -> STATE_JUDGING
 *
 * Memory optimization: The system uses shared memory for max_abs_diff,
 * threshold_matrix, and pixel_thresholds to reduce memory usage by ~6KB.
 *
 * <AUTHOR> Assistant
 * @date 2025-07-11
 */

#include "thermal_anomaly_core.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Example pre-threshold matrix (you would load this from file or define based on your needs)
static float example_pre_thresholds[THERMAL_HEIGHT][THERMAL_WIDTH] = {
    // Initialize with some reasonable values for learning stage
    // These are used during the learning phase to determine "problem frames"
    // You can customize these based on your specific application
};

// Function to initialize pre-thresholds with default values
static void init_example_pre_thresholds(void)
{
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            example_pre_thresholds[i][j] = 0.5f; // Default pre-threshold value
        }
    }
}

// Simulate getting a thermal frame (replace with actual MLX90640 reading)
static void simulate_thermal_frame(float frame[THERMAL_HEIGHT][THERMAL_WIDTH], int frame_num)
{
    // Generate some simulated thermal data
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            // Base temperature around 25°C with some variation
            frame[i][j] = 25.0f + (float)(rand() % 100) / 100.0f - 0.5f;
            
            // Add some anomalies occasionally
            if (frame_num > 100 && (rand() % 1000) < 5) {
                frame[i][j] += 5.0f; // Hot spot
            }
        }
    }
}

/**
 * @brief Example of using thermal detector with learning
 */
int thermal_learning_example(void)
{
    thermal_detector_t detector;
    thermal_config_t config;
    thermal_result_t result;
    int ret;
    
    printf("=== Thermal Anomaly Detection with Learning Example ===\n");
    
    // Initialize pre-thresholds
    init_example_pre_thresholds();
    
    // Create configuration with learning enabled
    thermal_detector_create_default_config(&config);
    config.enable_learning = true;
    config.warmup_time_ms = 1000; // 1 second warmup
    
    // Initialize detector with learning support
    ret = thermal_detector_init(&detector, &config, NULL, example_pre_thresholds);
    if (ret != 0) {
        printf("Failed to initialize detector: %d\n", ret);
        return -1;
    }
    
    printf("Detector initialized. Starting with state: %d\n", detector.state);
    printf("Learning enabled: %s\n", config.enable_learning ? "Yes" : "No");
    
    // Process frames through all stages
    int frame_count = 0;
    int max_frames = 2000; // Process up to 2000 frames
    
    while (frame_count < max_frames) {
        float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH];
        
        // Get thermal frame (simulated)
        simulate_thermal_frame(thermal_frame, frame_count);
        
        // Process frame with learning support
        ret = thermal_detector_process_frame_with_learning(&detector, thermal_frame, &result);
        if (ret != 0) {
            printf("Error processing frame %d: %d\n", frame_count, ret);
            break;
        }
        
        // Print progress every 100 frames
        if (frame_count % 100 == 0) {
            printf("Frame %d - State: %d, Progress: %.1f%%, Collected: %d, Consecutive Problems: %d\n",
                   frame_count, 
                   result.current_state,
                   thermal_detector_get_learning_progress(&detector) * 100.0f,
                   result.collected_frames,
                   result.consecutive_problem_frames);
        }
        
        // Print state transitions
        static thermal_detector_state_t last_state = STATE_WARMUP;
        if (result.current_state != last_state) {
            printf("State transition: %d -> %d at frame %d\n", 
                   last_state, result.current_state, frame_count);
            last_state = result.current_state;
            
            if (result.current_state == STATE_JUDGING && result.learning_completed) {
                printf("Learning completed! Generated threshold matrix.\n");
            }
        }
        
        // In judging stage, report anomalies
        if (result.current_state == STATE_JUDGING && result.anomaly_detected) {
            printf("Anomaly detected at frame %d: center(%.1f, %.1f), pixels: %d, trigger: %s\n",
                   frame_count, result.anomaly_center.x, result.anomaly_center.y,
                   result.anomaly_pixel_count, result.trigger_anomaly ? "YES" : "NO");
        }
        
        frame_count++;
        
        // Exit if learning is completed and we've processed some frames in judging stage
        if (result.current_state == STATE_JUDGING && result.learning_completed && frame_count > 1500) {
            printf("Example completed successfully after %d frames.\n", frame_count);
            break;
        }
    }
    
    // Print final statistics
    printf("\n=== Final Statistics ===\n");
    printf("Total frames processed: %d\n", frame_count);
    printf("Final state: %d\n", detector.state);
    printf("Learning completed: %s\n", detector.learning_completed ? "Yes" : "No");
    printf("Collected frames: %d\n", detector.collected_frames);
    printf("Learning progress: %.1f%%\n", thermal_detector_get_learning_progress(&detector) * 100.0f);
    
    return 0;
}

/**
 * @brief Example of skipping learning stage
 */
int thermal_skip_learning_example(void)
{
    thermal_detector_t detector;
    thermal_config_t config;
    thermal_result_t result;
    int ret;
    
    printf("\n=== Skip Learning Example ===\n");
    
    // Initialize pre-thresholds
    init_example_pre_thresholds();
    
    // Create configuration with learning disabled
    thermal_detector_create_default_config(&config);
    config.enable_learning = false; // Start directly in judging stage
    
    // Initialize detector
    ret = thermal_detector_init(&detector, &config, NULL, example_pre_thresholds);
    if (ret != 0) {
        printf("Failed to initialize detector: %d\n", ret);
        return -1;
    }
    
    printf("Detector initialized in judging mode (no learning)\n");
    printf("Current state: %d\n", detector.state);
    
    // Process a few frames
    for (int i = 0; i < 10; i++) {
        float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH];
        simulate_thermal_frame(thermal_frame, i);
        
        ret = thermal_detector_process_frame_with_learning(&detector, thermal_frame, &result);
        if (ret == 0) {
            printf("Frame %d processed - State: %d, Anomaly: %s\n", 
                   i, result.current_state, result.anomaly_detected ? "Yes" : "No");
        }
    }
    
    return 0;
}

// Uncomment the main function if you want to compile this as a standalone example
/*
int main(void)
{
    // Run learning example
    thermal_learning_example();
    
    // Run skip learning example
    thermal_skip_learning_example();
    
    return 0;
}
*/
