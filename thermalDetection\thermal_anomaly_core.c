/**
 * @file thermal_anomaly_core.c
 * @brief Core Thermal Anomaly Detection Algorithm Implementation
 * 
 * This file contains only the essential algorithm for processing
 * 32x24 thermal data arrays, based on thermal_anomaly_detector_personalized.py
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */

#include "thermal_anomaly_core.h"
#include "pre_pixel_thresholds.h"
#include <string.h>
#include <math.h>

// Library-declared mutable pixel thresholds memory
// This will be used for max_abs_diff during learning, then as final thresholds during judging
float pixel_thresholds[THERMAL_HEIGHT][THERMAL_WIDTH];

// todo： 后面可能要换为 历史平均值，而不是现在的滑动窗口

// Helper function prototypes
static bool pre_filter_frame(const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                            const float last_valid[THERMAL_HEIGHT][THERMAL_WIDTH],
                            const thermal_config_t* config,
                            bool has_last_valid);

static inline bool check_chess_mask(int i, int j, bool even_frame);

static void update_history_mean(thermal_detector_t* detector,
                              const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                              bool even_frame);

static bool detect_anomalies_in_region(const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      const float history_mean[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      const float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      bool even_frame,
                                      uint8_t min_pixels,
                                    //   bool anomaly_mask[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      anomaly_center_t* center,
                                      uint16_t* anomaly_count);

static float calculate_distance_squared(const anomaly_center_t* center1, const anomaly_center_t* center2);

// Learning stage function prototypes
static bool is_frame_invalid(const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH]);
static int init_window_fill(thermal_detector_t* detector);
static int noise_learning_process_frame(thermal_detector_t* detector,
                                       const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH]);
static void generate_threshold_matrix(thermal_detector_t* detector);
static int process_warmup_stage(thermal_detector_t* detector, thermal_result_t* result);
static int process_learning_stage(thermal_detector_t* detector,
                                const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                thermal_result_t* result);
static int process_judging_stage(thermal_detector_t* detector,
                               const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                               thermal_result_t* result);

int thermal_detector_init(thermal_detector_t* detector,
                         const thermal_config_t* config,
                         const float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH])
{
    if (!detector) {
        return -1; // Invalid argument
    }

    // Initialize configuration
    if (config) {
        detector->config = *config;
        // Ensure squared threshold is calculated
        detector->config.center_distance_threshold_squared =
            config->center_distance_threshold * config->center_distance_threshold;
    } else {
        thermal_detector_create_default_config(&detector->config);
    }

    // Initialize state machine
    detector->state = detector->config.enable_learning ? STATE_WARMUP : STATE_JUDGING;
    detector->warmup_start_time = 0; // Will be set when warmup starts

    // Use external pre_pixel_thresholds as pre_threshold
    detector->pre_threshold = pre_pixel_thresholds;

    // Initialize pixel_thresholds - use library-declared memory that can be modified
    if (thresholds) {
        detector->pixel_thresholds = (float (*)[THERMAL_WIDTH])thresholds;
    } else {
        // Use library-declared pixel_thresholds that can be modified during learning
        detector->pixel_thresholds = (float (*)[THERMAL_WIDTH])pixel_thresholds;
    }

    // Clear the pixel_thresholds memory for learning (will be used as max_abs_diff initially)
    memset(detector->pixel_thresholds, 0, THERMAL_HEIGHT * THERMAL_WIDTH * sizeof(float));

    // Initialize state
    memset(detector->history_buffer, 0, sizeof(detector->history_buffer));
    memset(detector->pixel_history_mean, 0, sizeof(detector->pixel_history_mean));
    memset(detector->pixel_history_sum, 0, sizeof(detector->pixel_history_sum));
    memset(detector->last_valid_frame, 0, sizeof(detector->last_valid_frame));

    detector->buffer_index = 0;
    detector->frames_stored = 0;
    detector->frame_count = 0;
    detector->is_initialized = false;
    detector->has_last_valid = false;

    // Initialize learning stage variables
    detector->collected_frames = 0;
    detector->consecutive_problem_frames = 0;
    detector->learning_completed = false;

    // Initialize anomaly centers
    detector->last_anomaly_centers[0].valid = false;
    detector->last_anomaly_centers[1].valid = false;

    return 0; // Success
}


// todo：这里后面多加一个参数输入，直接从MLX90640中读取现在读取的子页是哪一个。
// todo： 算了，无所谓了， 毕竟就是晚了一帧而已
int thermal_detector_process_frame(thermal_detector_t* detector,
                                  const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                  thermal_result_t* result)
{
    if (!detector || !thermal_frame || !result) {
        return -1; // Invalid argument
    }

    // Initialize result
    memset(result, 0, sizeof(thermal_result_t));
    // Note: Frame count increment removed to prevent double counting when called from state machine
    // Frame counting is now handled by thermal_detector_process_frame_with_learning()
    result->frame_count = detector->frame_count;
    result->is_even_frame = (detector->frame_count % 2 == 0);

    // Pre-filter check
    if (detector->config.enable_pre_filter) {
        bool is_valid = pre_filter_frame(thermal_frame, detector->last_valid_frame, 
                                       &detector->config, detector->has_last_valid);
        
        if (!is_valid) {
            result->is_filtered = true;
            return 0; // Frame filtered, but not an error
        }
    }

    // Update last valid frame
    memcpy(detector->last_valid_frame, thermal_frame, sizeof(detector->last_valid_frame));
    detector->has_last_valid = true;

    // Update history mean (using inline chess mask calculation)
    update_history_mean(detector, thermal_frame, result->is_even_frame);

    // // Count updated pixels using inline chess mask calculation
    // result->updated_pixels = 0;
    // for (int i = 0; i < THERMAL_HEIGHT; i++) {
    //     for (int j = 0; j < THERMAL_WIDTH; j++) {
    //         if (check_chess_mask(i, j, result->is_even_frame)) {
    //             result->updated_pixels++;
    //         }
    //     }
    // }

    // Detect anomalies in current region (optimized to return anomaly count)
    // bool anomaly_mask[THERMAL_HEIGHT][THERMAL_WIDTH];
    bool anomaly_detected = detect_anomalies_in_region(
        thermal_frame, detector->pixel_history_mean, detector->pixel_thresholds,
        result->is_even_frame, detector->config.min_anomaly_pixels,
        // anomaly_mask, 
        &result->anomaly_center, &result->anomaly_pixel_count);

    result->anomaly_detected = anomaly_detected;

    // Update anomaly center for current region
    int region_index = result->is_even_frame ? 0 : 1;
    if (anomaly_detected) {
        detector->last_anomaly_centers[region_index] = result->anomaly_center;
    } else {
        detector->last_anomaly_centers[region_index].valid = false;
    }

    // Copy centers to result
    result->even_center = detector->last_anomaly_centers[0];
    result->odd_center = detector->last_anomaly_centers[1];

    // Check trigger condition (both regions have anomalies and centers are close)
    result->trigger_anomaly = false;
    if (detector->last_anomaly_centers[0].valid && detector->last_anomaly_centers[1].valid) {
        float distance_squared = calculate_distance_squared(
            &detector->last_anomaly_centers[0], &detector->last_anomaly_centers[1]);

        // Use pre-calculated squared threshold for optimization
        if (distance_squared < detector->config.center_distance_threshold_squared) {
            result->trigger_anomaly = true;
        }
    }

    return 0; // Success
}

int thermal_detector_reset(thermal_detector_t* detector)
{
    if (!detector) {
        return -1; // Invalid argument
    }

    // Reset history data
    memset(detector->history_buffer, 0, sizeof(detector->history_buffer));
    memset(detector->pixel_history_mean, 0, sizeof(detector->pixel_history_mean));
    memset(detector->last_valid_frame, 0, sizeof(detector->last_valid_frame));
    
    detector->buffer_index = 0;
    detector->frames_stored = 0;
    detector->frame_count = 0;
    detector->is_initialized = false;
    detector->has_last_valid = false;
    
    // Reset anomaly centers
    detector->last_anomaly_centers[0].valid = false;
    detector->last_anomaly_centers[1].valid = false;

    return 0; // Success
}

// Process thermal frame from 1D array (optimized for MLX90640 direct output)
int thermal_detector_process_frame_1d(thermal_detector_t* detector,
                                     const float* thermal_frame_1d,
                                     thermal_result_t* result)
{
    if (!detector || !thermal_frame_1d || !result) {
        return -1; // Invalid parameters
    }

    // if (!detector->is_initialized && detector->frames_stored == 0) {
    //     return -2; // Detector not properly initialized
    // }

    // Convert 1D array to 2D format for existing algorithm
    // MLX90640 provides 768 values (32x24) in row-major order
    static float thermal_frame_2d[THERMAL_HEIGHT][THERMAL_WIDTH];
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            thermal_frame_2d[i][j] = thermal_frame_1d[i * THERMAL_WIDTH + j];
        }
    }

    // Increment frame count for 1D interface (since thermal_detector_process_frame no longer does it)
    detector->frame_count++;

    // Use existing 2D processing function
    return thermal_detector_process_frame(detector, thermal_frame_2d, result);
}

void thermal_detector_create_default_config(thermal_config_t* config)
{
    if (!config) return;

    config->pre_filter_min = DEFAULT_PRE_FILTER_MIN;
    config->pre_filter_max = DEFAULT_PRE_FILTER_MAX;
    config->pre_filter_delta = DEFAULT_PRE_FILTER_DELTA;
    config->window_size = DEFAULT_WINDOW_SIZE;
    config->center_distance_threshold = DEFAULT_CENTER_DISTANCE;
    config->center_distance_threshold_squared = DEFAULT_CENTER_DISTANCE * DEFAULT_CENTER_DISTANCE;
    config->min_anomaly_pixels = DEFAULT_MIN_ANOMALY_PIXELS;
    config->enable_pre_filter = true;
    config->enable_learning = true;
    config->warmup_time_ms = DEFAULT_WARMUP_TIME_MS;
}

void thermal_detector_create_default_thresholds(float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH],
                                               float threshold_value)
{
    if (!thresholds) return;
    
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            thresholds[i][j] = threshold_value;
        }
    }
}

// Helper function implementations

static bool pre_filter_frame(const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                            const float last_valid[THERMAL_HEIGHT][THERMAL_WIDTH],
                            const thermal_config_t* config,
                            bool has_last_valid)
{
    // Check temperature range
    float min_temp = thermal_frame[0][0];
    float max_temp = thermal_frame[0][0];
    
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            float temp = thermal_frame[i][j];
            if (temp < min_temp) min_temp = temp;
            if (temp > max_temp) max_temp = temp;
        }
    }
    
    if (min_temp < config->pre_filter_min || max_temp > config->pre_filter_max) {
        return false;
    }
    
    // Check temperature change (if we have a previous valid frame)
    if (has_last_valid) {
        float max_diff = 0.0f;
        
        for (int i = 0; i < THERMAL_HEIGHT; i++) {
            for (int j = 0; j < THERMAL_WIDTH; j++) {
                float diff = fabsf(thermal_frame[i][j] - last_valid[i][j]);
                if (diff > max_diff) max_diff = diff;
            }
        }
        
        if (max_diff > config->pre_filter_delta) {
            return false;
        }
    }
    
    return true;
}

// Optimized inline chess mask calculation
static inline bool check_chess_mask(int i, int j, bool even_frame)
{
    // Chess pattern: (i+j) parity determines update frame
    return even_frame ? ((i + j) % 2 == 0) : ((i + j) % 2 == 1);
}


// Optimized update_history_mean with inline chess mask calculation
// Optimized update_history_mean with circular buffer and running sum
static void update_history_mean(thermal_detector_t* detector,
                              const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                              bool even_frame)
{
    // Calculate the index of the frame that will be overwritten (if buffer is full)
    int old_frame_index = detector->buffer_index;
    bool buffer_full = (detector->frames_stored == detector->config.window_size);

    // Update running sum for chess mask pixels
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            // If buffer is full, subtract the old frame value
            if (buffer_full) {
                detector->pixel_history_sum[i][j] -= detector->history_buffer[old_frame_index][i][j];
            }

            // Add the new frame value
            detector->pixel_history_sum[i][j] += thermal_frame[i][j];

            // Calculate mean from running sum
            int count = buffer_full ? detector->config.window_size : (detector->frames_stored + 1);
            detector->pixel_history_mean[i][j] = detector->pixel_history_sum[i][j] / count;
        }
    }

    // Store current frame in circular buffer
    memcpy(detector->history_buffer[detector->buffer_index], thermal_frame,
           sizeof(detector->history_buffer[0]));

    // Update buffer index and frame count
    detector->buffer_index = (detector->buffer_index + 1) % detector->config.window_size;
    if (detector->frames_stored < detector->config.window_size) {
        detector->frames_stored++;
    }

    // Mark as initialized after first frame
    if (!detector->is_initialized) {
        detector->is_initialized = true;
    }
}


// Optimized detect_anomalies_in_region with integrated center calculation
static bool detect_anomalies_in_region(const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      const float history_mean[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      const float thresholds[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      bool even_frame,
                                      uint8_t min_pixels,
                                    //   bool anomaly_mask[THERMAL_HEIGHT][THERMAL_WIDTH],
                                      anomaly_center_t* center,
                                      uint16_t* anomaly_count)
{
    // Initialize anomaly mask and center calculation variables
    // memset(anomaly_mask, 0, sizeof(bool) * THERMAL_HEIGHT * THERMAL_WIDTH);
    center->valid = false;

    *anomaly_count = 0;
    float sum_x = 0.0f, sum_y = 0.0f;

    // Single pass: detect anomalies and calculate center simultaneously
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            if (check_chess_mask(i, j, even_frame)) {
                // Calculate temperature change (relative to history mean)
                float temp_change = fabsf(thermal_frame[i][j] - history_mean[i][j]);

                // Calculate change squared (thresholds already pre-processed)
                float change_squared = (temp_change * temp_change) ;// / 10.0f;

                // Check if anomaly
                if (change_squared > thresholds[i][j]) {
                    // anomaly_mask[i][j] = true;
                    (*anomaly_count)++;

                    // Accumulate center coordinates during detection
                    sum_y += i;  // Row index
                    sum_x += j;  // Column index
                }
            }
        }
    }

    // Check if we have enough anomaly pixels
    if (*anomaly_count < min_pixels) {
        return false;
    }

    // Calculate center from accumulated values (no additional loop needed)
    center->x = sum_x / (*anomaly_count);
    center->y = sum_y / (*anomaly_count);
    center->valid = true;

    return true;
}



// Optimized distance calculation without sqrt
static float calculate_distance_squared(const anomaly_center_t* center1, const anomaly_center_t* center2)
{
    if (!center1->valid || !center2->valid) {
        return INFINITY;
    }

    float dx = center1->x - center2->x;
    float dy = center1->y - center2->y;
    return dx * dx + dy * dy;
}

// ============================================================================
// Learning Stage Implementation
// ============================================================================

/**
 * @brief Check if frame is invalid (temperature out of range)
 */
static bool is_frame_invalid(const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH])
{
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            float temp = thermal_frame[i][j];
            if (temp < TEMP_MIN || temp > TEMP_MAX) {
                return true;
            }
        }
    }
    return false;
}

/**
 * @brief Initialize window filling for learning stage
 */
static int init_window_fill(thermal_detector_t* detector)
{
    // Reset window state
    detector->buffer_index = 0;
    detector->frames_stored = 0;
    detector->is_initialized = false;
    memset(detector->pixel_history_sum, 0, sizeof(detector->pixel_history_sum));
    memset(detector->pixel_history_mean, 0, sizeof(detector->pixel_history_mean));

    return 0; // Success - actual filling will be done by frame processing
}

/**
 * @brief Generate final threshold matrix from learning data
 */
static void generate_threshold_matrix(thermal_detector_t* detector)
{
    // pixel_thresholds currently contains max_abs_diff data
    // Transform it in-place to threshold_matrix
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            detector->pixel_thresholds[i][j] = THR_MULT * detector->pixel_thresholds[i][j];
        }
    }

    // pixel_thresholds now contains the final threshold matrix and is ready for judging
    detector->learning_completed = true;
}

/**
 * @brief Process one frame in learning stage
 */
static int noise_learning_process_frame(thermal_detector_t* detector,
                                       const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH])
{
    bool even_frame = (detector->frame_count % 2 == 0);

    // Use pixel_thresholds as max_abs_diff during learning stage
    float (*max_abs_diff)[THERMAL_WIDTH] = detector->pixel_thresholds;

    // If window is not full, fill it first
    if (detector->frames_stored < detector->config.window_size) {
        // Check if this frame would cause problems during window filling
        bool exceed = false;
        if (detector->frames_stored > 0) { // Only check if we have history
            for (int i = 0; i < THERMAL_HEIGHT && !exceed; i++) {
                for (int j = 0; j < THERMAL_WIDTH; j++) {
                    if (check_chess_mask(i, j, even_frame)) {
                        float mean = detector->pixel_history_mean[i][j];
                        if (fabsf(thermal_frame[i][j] - mean) > detector->pre_threshold[i][j]) {
                            exceed = true;
                            break;
                        }
                    }
                }
            }
        }

        if (exceed) {
            // Clear window and restart filling
            init_window_fill(detector);
        }

        // Add frame to window
        update_history_mean(detector, thermal_frame, even_frame);
        return 0; // Still filling window
    }

    // Window is full, now do learning data collection
    bool problem = false;

    // Check if this frame is a problem frame and update max_abs_diff
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            if (check_chess_mask(i, j, even_frame)) {
                float diff = fabsf(thermal_frame[i][j] - detector->pixel_history_mean[i][j]);
                if (diff > detector->pre_threshold[i][j]) {
                    problem = true;
                } else if (diff > max_abs_diff[i][j]) {
                    // Only update max_abs_diff for non-problem pixels
                    max_abs_diff[i][j] = diff;
                }
            }
        }
    }

    if (problem) {
        detector->consecutive_problem_frames++;
        // Don't update window or collected count
    } else {
        detector->consecutive_problem_frames = 0;
        // Update window and increment collected count
        update_history_mean(detector, thermal_frame, even_frame);
        detector->collected_frames++;
    }

    // Check for restart condition
    if (detector->consecutive_problem_frames >= RESTART_LRN) {
        detector->consecutive_problem_frames = 0;
        // Clear sliding window and restart window filling process
        // Keep collected_frames and max_abs_diff (pixel_thresholds), just reset window
        init_window_fill(detector);
    }

    // Check for completion
    if (detector->collected_frames >= MAX_COLLECT) {
        generate_threshold_matrix(detector);
        detector->state = STATE_JUDGING;
        return 1; // Learning completed
    }

    return 0; // Continue learning
}

/**
 * @brief Process warmup stage
 */
static int process_warmup_stage(thermal_detector_t* detector, thermal_result_t* result)
{
    // Simple time-based warmup (implementation dependent)
    // For now, just transition immediately to learning
    // In real implementation, you might check elapsed time
    // todo：在函数中加上等待， 或者在外部加上等待
    detector->state = STATE_LEARNING;
    init_window_fill(detector);

    result->current_state = STATE_LEARNING;
    return 0;
}

/**
 * @brief Process learning stage
 */
static int process_learning_stage(thermal_detector_t* detector,
                                const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                thermal_result_t* result)
{
    int learning_result = noise_learning_process_frame(detector, thermal_frame);

    result->current_state = detector->state;
    result->collected_frames = detector->collected_frames;
    result->consecutive_problem_frames = detector->consecutive_problem_frames;
    result->learning_completed = detector->learning_completed;

    if (learning_result == 1) {
        // Learning completed, state already changed to STATE_JUDGING
        result->current_state = STATE_JUDGING;
    }

    return 0;
}

/**
 * @brief Process judging stage (uses existing high-performance code)
 */
static int process_judging_stage(thermal_detector_t* detector,
                               const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                               thermal_result_t* result)
{
    // Use existing high-performance judging logic
    int ret = thermal_detector_process_frame(detector, thermal_frame, result);

    // Add state information
    result->current_state = STATE_JUDGING;
    result->collected_frames = detector->collected_frames;
    result->consecutive_problem_frames = detector->consecutive_problem_frames;
    result->learning_completed = detector->learning_completed;

    // Check for reconstruction condition (consecutive problem frames in judging stage)
    if (result->is_filtered || !result->anomaly_detected) {
        detector->consecutive_problem_frames = 0;
    } else {
        detector->consecutive_problem_frames++;
        if (detector->consecutive_problem_frames >= RESTART_JUD) {
            // Trigger learning reconstruction
            detector->state = STATE_LEARNING;
            detector->collected_frames = 0;
            detector->consecutive_problem_frames = 0;
            detector->learning_completed = false;
            memset(detector->pixel_thresholds, 0, THERMAL_HEIGHT * THERMAL_WIDTH * sizeof(float)); // Clear pixel_thresholds for new learning
            init_window_fill(detector);
            result->current_state = STATE_LEARNING;
        }
    }

    return ret;
}

// ============================================================================
// Public API Functions with Learning Support
// ============================================================================

/**
 * @brief Process frame with full state machine support
 */
int thermal_detector_process_frame_with_learning(thermal_detector_t* detector,
                                               const float thermal_frame[THERMAL_HEIGHT][THERMAL_WIDTH],
                                               thermal_result_t* result)
{
    if (!detector || !thermal_frame || !result) {
        return -1; // Invalid argument
    }

    // Initialize result
    memset(result, 0, sizeof(thermal_result_t));
    detector->frame_count++;
    result->frame_count = detector->frame_count;
    result->is_even_frame = (detector->frame_count % 2 == 0);
    result->current_state = detector->state;

    // Process based on current state
    switch (detector->state) {
        case STATE_WARMUP:
            return process_warmup_stage(detector, result);

        case STATE_LEARNING:
            return process_learning_stage(detector, thermal_frame, result);

        case STATE_JUDGING:
            return process_judging_stage(detector, thermal_frame, result);

        default:
            return -2; // Invalid state
    }
}

/**
 * @brief Process frame with full state machine support from 1D array
 */
int thermal_detector_process_frame_1d_with_learning(thermal_detector_t* detector,
                                                  const float* thermal_frame_1d,
                                                  thermal_result_t* result)
{
    if (!detector || !thermal_frame_1d || !result) {
        return -1; // Invalid parameters
    }

    // Convert 1D array to 2D format for existing algorithm
    static float thermal_frame_2d[THERMAL_HEIGHT][THERMAL_WIDTH];
    for (int i = 0; i < THERMAL_HEIGHT; i++) {
        for (int j = 0; j < THERMAL_WIDTH; j++) {
            thermal_frame_2d[i][j] = thermal_frame_1d[i * THERMAL_WIDTH + j];
        }
    }

    // Use existing 2D processing function with learning
    return thermal_detector_process_frame_with_learning(detector, thermal_frame_2d, result);
}

/**
 * @brief Get current learning progress
 */
float thermal_detector_get_learning_progress(const thermal_detector_t* detector)
{
    if (!detector) {
        return 0.0f;
    }

    if (detector->state == STATE_WARMUP) {
        return 0.0f;
    } else if (detector->state == STATE_JUDGING && detector->learning_completed) {
        return 1.0f;
    } else if (detector->state == STATE_LEARNING) {
        return (float)detector->collected_frames / MAX_COLLECT;
    }

    return 0.0f;
}

/**
 * @brief Force transition to judging stage (skip learning)
 */
int thermal_detector_skip_learning(thermal_detector_t* detector)
{
    if (!detector) {
        return -1;
    }

    detector->state = STATE_JUDGING;
    detector->learning_completed = true;

    // Copy pre_threshold to pixel_thresholds and use it as final threshold
    memcpy(detector->pixel_thresholds, detector->pre_threshold, THERMAL_HEIGHT * THERMAL_WIDTH * sizeof(float));

    return 0;
}
