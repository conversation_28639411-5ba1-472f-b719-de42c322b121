/**
 * @file multi_sensor_init.c
 * @brief Multi-sensor system initialization sequence implementation
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-22
 */

#include "multi_sensor_init.h"
#include "mlx90640_i2c_multi.h"
#include "MLX90640_API.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "MULTI_SENSOR_INIT";

// Initialization step names for logging (simplified - 4 independent I2C buses)
static const char* init_step_names[] = {
    "4 Independent I2C Bus Initialization",
    "Sensor Detection on All Buses",
    "Hardware Setup Complete"
};

#define INIT_STEP_COUNT (sizeof(init_step_names) / sizeof(init_step_names[0]))

// ============================================================================
// Main Initialization Function
// ============================================================================

esp_err_t multi_sensor_hardware_init(void)
{
    esp_err_t ret;
    uint8_t current_step = 0;

    MULTI_SENSOR_LOGI(TAG, "Starting 4-sensor hardware initialization sequence");
    MULTI_SENSOR_LOGI(TAG, "Target configuration: 4 independent I2C buses, all sensors at address 0x33");
    MULTI_SENSOR_LOGI(TAG, "  - Sensor 0: Hardware I2C Bus 0 (GPIO 21,22)");
    MULTI_SENSOR_LOGI(TAG, "  - Sensor 1: Hardware I2C Bus 1 (GPIO 18,19)");
    MULTI_SENSOR_LOGI(TAG, "  - Sensor 2: Software I2C Bus 2 (GPIO 4,5)");
    MULTI_SENSOR_LOGI(TAG, "  - Sensor 3: Software I2C Bus 3 (GPIO 16,17)");

    // Step 1: Initialize all 4 I2C buses (2 hardware + 2 software)
    multi_sensor_log_init_progress(current_step++, INIT_STEP_COUNT, "4 Independent I2C Bus Initialization");
    ret = multi_sensor_retry_init_step(multi_sensor_i2c_init, MAX_INIT_RETRIES, "I2C Init");
    if (ret != ESP_OK) {
        return multi_sensor_handle_init_failure(ret, "I2C initialization", false);
    }

    // Step 2: Detect sensors on all buses
    multi_sensor_log_init_progress(current_step++, INIT_STEP_COUNT, "Sensor Detection on All Buses");
    ret = multi_sensor_detect_all_sensors();
    if (ret != ESP_OK) {
        return multi_sensor_handle_init_failure(ret, "Sensor detection", false);
    }

    multi_sensor_log_init_progress(current_step++, INIT_STEP_COUNT, "Hardware Setup Complete");

    MULTI_SENSOR_LOGI(TAG, "4-sensor hardware initialization completed successfully");
    MULTI_SENSOR_LOGI(TAG, "All sensors ready on independent I2C buses");
    return ESP_OK;
}

// ============================================================================
// Individual Initialization Steps
// ============================================================================

esp_err_t multi_sensor_detect_all_sensors(void)
{
    MULTI_SENSOR_LOGI(TAG, "Detecting sensors on all 4 I2C buses");

    bool all_detected = true;

    // Check each sensor on its dedicated I2C bus
    for (sensor_id_t sensor_id = SENSOR_0; sensor_id < SENSOR_COUNT; sensor_id++) {
        i2c_bus_type_t bus_type;
        uint8_t slave_addr;

        esp_err_t ret = mlx90640_sensor_to_i2c_params(sensor_id, &bus_type, &slave_addr);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to get I2C params for sensor %d", sensor_id);
            all_detected = false;
            continue;
        }

        bool detected = mlx90640_sensor_detect_unified(bus_type, slave_addr);
        if (detected) {
            MULTI_SENSOR_LOGI(TAG, "✓ Sensor %d detected on %s (address 0x%02X)",
                             sensor_id, mlx90640_get_i2c_bus_name(bus_type), slave_addr);
        } else {
            MULTI_SENSOR_LOGW(TAG, "✗ Sensor %d NOT detected on %s (address 0x%02X)",
                             sensor_id, mlx90640_get_i2c_bus_name(bus_type), slave_addr);
            all_detected = false;
        }
    }

    if (all_detected) {
        MULTI_SENSOR_LOGI(TAG, "All 4 sensors detected successfully");
        return ESP_OK;
    } else {
        MULTI_SENSOR_LOGE(TAG, "Some sensors not detected - check connections");
        return ESP_ERR_NOT_FOUND;
    }
}

esp_err_t multi_sensor_i2c_init(void)
{
    MULTI_SENSOR_LOGI(TAG, "Initializing 4 independent I2C buses (2 hardware + 2 software)");

    esp_err_t ret = mlx90640_i2c_multi_init();
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "I2C multi-bus initialization failed: %s", esp_err_to_name(ret));
        return ret;
    }

    multi_sensor_init_delay(I2C_INIT_DELAY_MS, "I2C bus stabilization");

    MULTI_SENSOR_LOGI(TAG, "All 4 I2C buses initialized successfully");
    MULTI_SENSOR_LOGI(TAG, "  - Hardware I2C Bus 0: GPIO %d(SDA), %d(SCL)", I2C_HW0_SDA_GPIO, I2C_HW0_SCL_GPIO);
    MULTI_SENSOR_LOGI(TAG, "  - Hardware I2C Bus 1: GPIO %d(SDA), %d(SCL)", I2C_HW1_SDA_GPIO, I2C_HW1_SCL_GPIO);
    MULTI_SENSOR_LOGI(TAG, "  - Software I2C Bus 2: GPIO %d(SDA), %d(SCL)", I2C_SW2_SDA_GPIO, I2C_SW2_SCL_GPIO);
    MULTI_SENSOR_LOGI(TAG, "  - Software I2C Bus 3: GPIO %d(SDA), %d(SCL)", I2C_SW3_SDA_GPIO, I2C_SW3_SCL_GPIO);
    return ESP_OK;
}

esp_err_t multi_sensor_hardware_setup(void)
{
    MULTI_SENSOR_LOGI(TAG, "Setting up hardware for all 4 sensors");

    esp_err_t ret;
    bool all_initialized = true;

    // Initialize each sensor on its dedicated I2C bus
    for (sensor_id_t sensor_id = SENSOR_0; sensor_id < SENSOR_COUNT; sensor_id++) {
        i2c_bus_type_t bus_type;
        uint8_t slave_addr;

        ret = mlx90640_sensor_to_i2c_params(sensor_id, &bus_type, &slave_addr);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to get I2C params for sensor %d", sensor_id);
            all_initialized = false;
            continue;
        }

        MULTI_SENSOR_LOGI(TAG, "Initializing sensor %d on %s (address 0x%02X)",
                         sensor_id, mlx90640_get_i2c_bus_name(bus_type), slave_addr);

        // Initialize sensor hardware (this function needs to be updated to use unified I2C)
        ret = multi_sensor_init_single_sensor(sensor_id);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to initialize sensor %d: %s",
                             sensor_id, esp_err_to_name(ret));
            all_initialized = false;
        } else {
            MULTI_SENSOR_LOGI(TAG, "✓ Sensor %d initialized successfully", sensor_id);
        }

        multi_sensor_init_delay(SENSOR_INIT_DELAY_MS, "Sensor stabilization");
    }

    if (all_initialized) {
        MULTI_SENSOR_LOGI(TAG, "All 4 sensors hardware setup completed successfully");
        return ESP_OK;
    } else {
        MULTI_SENSOR_LOGE(TAG, "Some sensors failed to initialize");
        return ESP_ERR_INVALID_STATE;
    }
}

esp_err_t multi_sensor_init_single_sensor(sensor_id_t sensor_id)
{
    // 系统级初始化：只做基础检测，MLX90640特定配置在mlx90640_instance_init_hardware()中完成
    i2c_bus_type_t bus_type;
    uint8_t slave_addr;

    esp_err_t ret = mlx90640_sensor_to_i2c_params(sensor_id, &bus_type, &slave_addr);
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGE(TAG, "Failed to get I2C params for sensor %d", sensor_id);
        return ret;
    }

    MULTI_SENSOR_LOGD(TAG, "Basic detection for sensor %d on %s",
                     sensor_id, mlx90640_get_i2c_bus_name(bus_type));

    // 系统级初始化只验证传感器基础通信，不做MLX90640特定配置
    // MLX90640特定配置（EEPROM、刷新率、模式等）在实例初始化时完成
    if (!mlx90640_sensor_detect_unified(bus_type, slave_addr)) {
        MULTI_SENSOR_LOGE(TAG, "Sensor %d not responding during basic detection", sensor_id);
        return ESP_ERR_NOT_FOUND;
    }

    MULTI_SENSOR_LOGD(TAG, "Sensor %d basic detection successful", sensor_id);
    return ESP_OK;
}

esp_err_t multi_sensor_verify_all_sensors(void)
{
    MULTI_SENSOR_LOGI(TAG, "Verifying communication with all 4 sensors");

    bool all_verified = true;

    // Verify each sensor on its dedicated I2C bus
    for (sensor_id_t sensor_id = SENSOR_0; sensor_id < SENSOR_COUNT; sensor_id++) {
        i2c_bus_type_t bus_type;
        uint8_t slave_addr;

        esp_err_t ret = mlx90640_sensor_to_i2c_params(sensor_id, &bus_type, &slave_addr);
        if (ret != ESP_OK) {
            MULTI_SENSOR_LOGE(TAG, "Failed to get I2C params for sensor %d", sensor_id);
            all_verified = false;
            continue;
        }

        // Verify sensor communication
        bool verified = mlx90640_sensor_detect_unified(bus_type, slave_addr);
        if (verified) {
            MULTI_SENSOR_LOGI(TAG, "✓ Sensor %d communication verified on %s",
                             sensor_id, mlx90640_get_i2c_bus_name(bus_type));
        } else {
            MULTI_SENSOR_LOGE(TAG, "✗ Sensor %d communication failed on %s",
                             sensor_id, mlx90640_get_i2c_bus_name(bus_type));
            all_verified = false;
        }
    }

    if (all_verified) {
        MULTI_SENSOR_LOGI(TAG, "All 4 sensors verified successfully");
        return ESP_OK;
    } else {
        MULTI_SENSOR_LOGE(TAG, "Some sensors failed verification");
        return ESP_ERR_INVALID_STATE;
    }
}

// This function was moved earlier in the file and is now part of the main initialization sequence

// ============================================================================
// Additional Utility Functions
// ============================================================================

esp_err_t multi_sensor_test_communication(void)
{
    MULTI_SENSOR_LOGI(TAG, "Testing I2C communication with all sensors");

    // Test basic communication with each sensor
    for (sensor_id_t sensor_id = 0; sensor_id < SENSOR_COUNT; sensor_id++) {
        i2c_bus_type_t bus_type;
        uint8_t slave_addr;
        esp_err_t ret = mlx90640_sensor_to_i2c_params(sensor_id, &bus_type, &slave_addr);
        if (ret != ESP_OK) {
            continue;
        }

        // Try to read a register to test communication
        uint16_t test_data;
        int result = mlx90640_i2c_unified_read(bus_type, slave_addr, 0x2407, 1, &test_data);
        if (result != 0) {
            MULTI_SENSOR_LOGE(TAG, "Communication test failed for sensor %s",
                             mlx90640_get_sensor_name(sensor_id));
            return ESP_ERR_INVALID_RESPONSE;
        }

        MULTI_SENSOR_LOGI(TAG, "Communication test passed for sensor %s (data: 0x%04X)",
                         mlx90640_get_sensor_name(sensor_id), test_data);
    }

    MULTI_SENSOR_LOGI(TAG, "All communication tests passed");
    return ESP_OK;
}

// ============================================================================
// Cleanup Functions
// ============================================================================

esp_err_t multi_sensor_hardware_deinit(void)
{
    MULTI_SENSOR_LOGI(TAG, "Deinitializing hardware resources");

    // Deinitialize all 4 I2C buses (2 hardware + 2 software)
    esp_err_t ret = mlx90640_i2c_multi_deinit();
    if (ret != ESP_OK) {
        MULTI_SENSOR_LOGW(TAG, "I2C deinitialization failed: %s", esp_err_to_name(ret));
    }

    MULTI_SENSOR_LOGI(TAG, "Hardware deinitialization completed");
    return ESP_OK;
}

esp_err_t multi_sensor_reset_all(void)
{
    MULTI_SENSOR_LOGI(TAG, "Resetting all sensors to default state");

    // In the 4-independent-I2C architecture, no GPIO switching is needed
    // All sensors are always powered and accessible on their dedicated buses

    multi_sensor_init_delay(100, "Sensor reset");

    MULTI_SENSOR_LOGI(TAG, "All sensors reset to default state");
    return ESP_OK;
}

// ============================================================================
// Utility Functions
// ============================================================================

const char* multi_sensor_get_init_step_name(uint8_t step)
{
    if (step < INIT_STEP_COUNT) {
        return init_step_names[step];
    }
    return "Unknown Step";
}

void multi_sensor_log_init_progress(uint8_t step, uint8_t total_steps, const char* step_name)
{
    MULTI_SENSOR_LOGI(TAG, "[%d/%d] %s", step + 1, total_steps, step_name);
}

void multi_sensor_init_delay(uint32_t delay_ms, const char* reason)
{
    MULTI_SENSOR_LOGD(TAG, "Delay %ums: %s", (unsigned)delay_ms, reason);
    vTaskDelay(pdMS_TO_TICKS(delay_ms));
}

// ============================================================================
// Error Recovery Functions
// ============================================================================

esp_err_t multi_sensor_retry_init_step(esp_err_t (*init_func)(void),
                                      uint8_t max_retries,
                                      const char* step_name)
{
    esp_err_t ret;
    uint8_t retry_count = 0;

    do {
        ret = init_func();
        if (ret == ESP_OK) {
            if (retry_count > 0) {
                MULTI_SENSOR_LOGI(TAG, "%s succeeded after %d retries", step_name, retry_count);
            }
            return ESP_OK;
        }

        retry_count++;
        if (retry_count <= max_retries) {
            MULTI_SENSOR_LOGW(TAG, "%s failed (attempt %d/%d): %s",
                             step_name, retry_count, max_retries + 1, esp_err_to_name(ret));
            vTaskDelay(pdMS_TO_TICKS(100 * retry_count)); // Exponential backoff
        }
    } while (retry_count <= max_retries);

    MULTI_SENSOR_LOGE(TAG, "%s failed after %d attempts", step_name, max_retries + 1);
    return ret;
}

esp_err_t multi_sensor_handle_init_failure(esp_err_t error_code,
                                          const char* step_name,
                                          bool recovery_possible)
{
    MULTI_SENSOR_LOGE(TAG, "Initialization failure in step '%s': %s",
                     step_name, esp_err_to_name(error_code));

    if (recovery_possible) {
        MULTI_SENSOR_LOGI(TAG, "Attempting recovery...");
        multi_sensor_reset_all();
        // Could add more recovery logic here
    }

    return error_code;
}
